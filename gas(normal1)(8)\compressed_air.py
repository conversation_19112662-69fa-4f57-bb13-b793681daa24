import tkinter as tk
from tkinter import ttk, messagebox
import math
import json
import os
import traceback
from datetime import datetime

class CompressedAirCalculator:
    # """
    # 压缩空气计算类 - 封装所有与压缩空气计算相关的功能
    # """
    # _instance = None
    
    # @classmethod
    # def get_instance(cls):
    #     if cls._instance is None:
    #         # 如果没有实例，返回一个空对象，供调用时使用
    #         cls._instance = cls(None)
    #     return cls._instance
    def __init__(self, parent):
        """
        初始化压缩空气计算器
        
        参数:
            parent: 父窗口对象，通常是GasCalculator实例
        """
        self.parent = parent
        self.air_window = None
        self.branch_tabs = []  # 保存所有压缩空气管标签页信息
        self.branch_tab_count = 0
        
        # 初始化数据存储
        self._data = {}
        
        # 从父对象中获取history_file路径，如果不存在则创建默认路径
        if parent and hasattr(parent, 'history_file'):
            self.history_file = parent.history_file
        elif parent and hasattr(parent, 'app_data_dir'):
            self.history_file = os.path.join(parent.app_data_dir, 'history.json')
        else:
            # 默认路径
            app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'GasCalculator')
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
            self.history_file = os.path.join(app_data_dir, 'history.json')
        
        # 创建变量字典，存储各管道的所有参数
        self.branch_data = {}
        
        # 初始化变量
        self.init_variables()
        
        # 初始化主窗口和UI控件引用
        self.tab_control = None
        self.delete_branch_button = None
        
    def init_variables(self):
        """初始化所有相关变量"""
        # 基本参数
        self.air_pressure = tk.StringVar(value="")
        self.air_velocity = tk.StringVar(value="")
        self.air_continuous_total = tk.StringVar(value="")
        self.air_intermittent_total = tk.StringVar(value="")
        self.air_total_flow = tk.StringVar(value="")
        self.air_calculated_diameter = tk.StringVar(value="")
        self.air_standard_diameter = tk.StringVar(value="")
        self.air_actual_velocity = tk.StringVar(value="")
        
        # 用气明细变量
        self.air_input_vars = {
            "regenerator_purge": tk.StringVar(value=""),  # 蓄热室吹扫用气
            "gun_cooling": tk.StringVar(value=""),  # 喷枪冷却用气
            "feeder": tk.StringVar(value=""),  # 投料机用气
            "valve_tv": tk.StringVar(value=""),  # 阀及工业电视用气
            "cold_end": tk.StringVar(value=""),  # 冷端机组用气
            "annealing_ir": tk.StringVar(value=""),  # 退火窑及红外用气
            "branch_heating": tk.StringVar(value=""),  # 支通路加热用气
            "rolling_burn": tk.StringVar(value=""),  # 压延机烧边火用气
            "rolling_clean": tk.StringVar(value="")  # 压延机清理用气
        }
        
        # 支管1变量
        self.air_branch1_description = tk.StringVar(value="")
        self.air_branch1_flow = tk.StringVar(value="")
        self.air_branch1_pressure = tk.StringVar(value="")
        self.air_branch1_diameter = tk.StringVar(value="")
        self.air_branch1_selected_diameter = tk.StringVar(value="")
        self.air_branch1_actual_velocity = tk.StringVar(value="")

        # 支管2变量
        self.air_branch2_description = tk.StringVar(value="")
        self.air_branch2_flow = tk.StringVar(value="")
        self.air_branch2_pressure = tk.StringVar(value="")
        self.air_branch2_diameter = tk.StringVar(value="")
        self.air_branch2_selected_diameter = tk.StringVar(value="")
        self.air_branch2_actual_velocity = tk.StringVar(value="")

        # 支管3变量
        self.air_branch3_description = tk.StringVar(value="")
        self.air_branch3_flow = tk.StringVar(value="")
        self.air_branch3_pressure = tk.StringVar(value="")
        self.air_branch3_diameter = tk.StringVar(value="")
        self.air_branch3_selected_diameter = tk.StringVar(value="")
        self.air_branch3_actual_velocity = tk.StringVar(value="")
        
    def show_window(self):
        """显示压缩空气管道计算窗口"""
        # 如果窗口已经打开，则聚焦到该窗口
        if hasattr(self, 'air_window') and self.air_window and self.air_window.winfo_exists():
            self.air_window.focus_set()
            return
            
        # 创建顶层窗口
        self.air_window = tk.Toplevel(self.parent.root)
        air_window = self.air_window  # 创建本地变量方便引用
        air_window.title("压缩空气管道计算")
        air_window.resizable(True, True)
        
        # 重要：先隐藏窗口，避免闪烁
        air_window.withdraw()
        
        # 设置窗口大小
        air_window.geometry("800x500")
        
        # 创建主框架
        main_frame = ttk.Frame(air_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建选项卡控件用于切换不同的压缩空气管
        self.tab_control = ttk.Notebook(main_frame)
        self.tab_control.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建用气需求输入选项卡
        tab_main = ttk.Frame(self.tab_control)
        self.tab_control.add(tab_main, text="用气需求输入")
        
        # 创建用气需求输入选项卡的内容
        self.create_main_tab(tab_main)
        
        # 不在这里创建第一个标签页，而是在load_data_from_current_project中创建
        # self._create_first_branch_tab()
        
        # 添加按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建删除按钮
        self.delete_branch_button = ttk.Button(
            button_frame, 
            text="删除当前压缩空气管", 
            command=self.delete_current_branch_tab,
            state="disabled"  # 初始时禁用
        )
        self.delete_branch_button.pack(side="left", padx=5)
        
        # 添加"新增"按钮
        ttk.Button(
            button_frame, 
            text="新增压缩空气管", 
            command=self.add_new_branch_tab
        ).pack(side="left", padx=5)
        
        # # 添加"保存"按钮
        # ttk.Button(
        #     button_frame, 
        #     text="保存数据", 
        #     command=self.save_data_with_message
        # ).pack(side="right", padx=5)
        
        # # 添加"关闭"按钮
        # ttk.Button(
        #     button_frame, 
        #     text="关闭", 
        #     command=self.close_window
        # ).pack(side="right", padx=5)
        
        # 在窗口创建后，强制重新加载当前项目的数据
        if hasattr(self.parent, 'project_name') and hasattr(self.parent, 'project_code'):
            project_name = self.parent.project_name.get()
            project_code = self.parent.project_code.get()
            current_project_id = f"{project_name}_{project_code}"
            
            # 如果内存中有当前项目的数据，优先使用
            data_loaded = False
            if hasattr(self.parent, 'saved_air_data') and isinstance(self.parent.saved_air_data, dict) and \
               current_project_id in self.parent.saved_air_data:
                data = self.parent.saved_air_data[current_project_id]
                print(f"从内存加载项目数据: {current_project_id}")
                
                # 直接检查数据包含多少支管
                branch_count = 0
                branch_keys = []
                
                # 检查压缩空气支管数据列表
                if "压缩空气支管数据" in data and isinstance(data["压缩空气支管数据"], list):
                    branch_count = len(data["压缩空气支管数据"])
                    print(f"找到 {branch_count} 个支管数据")
                    
                    # 清空已有支管数据
                    self.branch_data = {}
                    self.branch_tab_count = 0
                    
                    # 清除已有标签页，保留第一个"用气需求输入"标签页
                    if hasattr(self, 'tab_control') and self.tab_control:
                        tabs = self.tab_control.tabs()
                        for tab in tabs[1:]:
                            self.tab_control.forget(tab)
                    
                    # 读取并创建所有支管标签页
                    for branch in data["压缩空气支管数据"]:
                        idx = branch.get("支管编号", self.branch_tab_count + 1)
                        self.branch_tab_count = max(self.branch_tab_count, idx)
                        
                        # 创建支管数据变量
                        self.branch_data[idx] = {
                            "description": tk.StringVar(value=branch.get("支管描述", "")),
                            "flow": tk.StringVar(value=branch.get("流量", "")),
                            "pressure": tk.StringVar(value=branch.get("压力", "")),
                            "calculated_diameter": tk.StringVar(value=branch.get("计算管径", "")),
                            "selected_diameter": tk.StringVar(value=branch.get("选取管径", "")),
                            "actual_velocity": tk.StringVar(value=branch.get("实际流速", ""))
                        }
                        
                        # 创建标签页
                        new_tab = ttk.Frame(self.tab_control)
                        self.tab_control.add(new_tab, text=f"压缩空气管{idx}")
                        self.create_branch_tab(new_tab, idx)
                        print(f"已创建支管{idx}标签页")
                    
                    # 更新删除按钮状态
                    if hasattr(self, 'delete_branch_button'):
                        if self.branch_tab_count > 1:
                            self.delete_branch_button.config(state="normal")
                        else:
                            self.delete_branch_button.config(state="disabled")
                    
                    # 加载基本数据和用气明细
                    self._load_basic_data(data)
                    self._load_detail_data(data)
                    data_loaded = True
                
                # 如果没有找到支管数据列表，尝试从单独字段加载
                if not data_loaded:
                    self.load_data_from_record(data)
                    data_loaded = True
        
        # 如果没有加载到数据，创建默认支管
        if not hasattr(self, 'branch_data') or not self.branch_data:
            self._create_first_branch_tab()
        
        # 剩余窗口设置代码保持不变
        # ...
        
        # 绑定窗口关闭事件
        air_window.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # 设置变量跟踪
        self.setup_variable_trace()
        
        # 窗口居中显示
        self.center_window(air_window)
        
        # 强制更新窗口以确保所有组件正确布局
        air_window.update_idletasks()
        
        # 最后显示窗口，避免闪烁
        air_window.deiconify()
        
    def _create_first_branch_tab(self):
        """创建第一个压缩空气管标签页"""
        # 增加计数
        self.branch_tab_count = 1
        
        # 初始化第一个支管的数据变量
        self.branch_data[1] = {
            "description": tk.StringVar(value=""),
            "flow": tk.StringVar(value=""),
            "pressure": tk.StringVar(value=""),
            "calculated_diameter": tk.StringVar(value=""),
            "selected_diameter": tk.StringVar(value=""),
            "actual_velocity": tk.StringVar(value="")
        }
        
        # 创建标签页
        branch1_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(branch1_tab, text=f"压缩空气管1")
        
        # 创建压缩空气管1界面
        self.create_branch_tab(branch1_tab, 1)
        
    def add_new_branch_tab(self):
        """添加新的压缩空气管标签页"""
        # 增加计数
        self.branch_tab_count += 1
        tab_index = self.branch_tab_count
        
        # 初始化新支管的数据变量
        self.branch_data[tab_index] = {
            "description": tk.StringVar(value=""),
            "flow": tk.StringVar(value=""),
            "pressure": tk.StringVar(value=""),
            "calculated_diameter": tk.StringVar(value=""),
            "selected_diameter": tk.StringVar(value=""),
            "actual_velocity": tk.StringVar(value="")
        }
        
        # 创建新标签页
        new_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(new_tab, text=f"压缩空气管{tab_index}")
        
        # 创建标签页的界面
        self.create_branch_tab(new_tab, tab_index)
        
        # 启用删除按钮
        if hasattr(self, 'delete_branch_button'):
            self.delete_branch_button.config(state="normal")
        
        # 切换到新创建的标签页
        self.tab_control.select(new_tab)
        
        # 立即保存到内存中
        if hasattr(self.parent, 'project_name') and hasattr(self.parent, 'project_code'):
            try:
                # 收集当前数据
                air_data = self.collect_data()
                
                # 获取当前项目ID
                project_name = self.parent.project_name.get()
                project_code = self.parent.project_code.get()
                current_project_id = f"{project_name}_{project_code}"
                
                # 保存到内存
                if hasattr(self.parent, 'saved_air_data'):
                    if not isinstance(self.parent.saved_air_data, dict):
                        self.parent.saved_air_data = {}
                    self.parent.saved_air_data[current_project_id] = air_data
                    print(f"新建支管后已更新内存数据: {current_project_id}, 支管总数: {len(air_data.get('压缩空气支管数据', []))}")
            except Exception as e:
                print(f"新建支管后保存数据出错: {str(e)}")
        
        print(f"已创建新压缩空气管标签页: {tab_index}, 当前支管总数: {len(self.branch_data)}")
        
    def delete_current_branch_tab(self):
        """删除当前压缩空气管标签页"""
        # 获取当前选中的标签页
        current_tab = self.tab_control.select()
        
        # 如果当前选中的是第一个标签页（用气需求输入），不执行删除
        if current_tab == self.tab_control.tabs()[0]:
            messagebox.showinfo("提示", "不能删除用气需求输入标签页")
            return
        
        # 获取标签页文本和索引
        tab_text = self.tab_control.tab(current_tab, "text")
        tab_idx = self.tab_control.index(current_tab)
        print(f"删除标签页: {tab_text}, 索引: {tab_idx}")
        
        # 提取支管编号
        branch_index = None
        for idx in self.branch_data:
            if f"压缩空气管{idx}" == tab_text:
                branch_index = idx
                break
        
        if branch_index is not None:
            # 删除支管数据
            if branch_index in self.branch_data:
                del self.branch_data[branch_index]
                print(f"已删除支管{branch_index}数据")
        
        # 删除标签页
        self.tab_control.forget(current_tab)
        
        # 更新branch_tab_count
        self.branch_tab_count = len(self.branch_data)
        
        # 如果只剩下一个支管标签页，禁用删除按钮
        if len(self.tab_control.tabs()) <= 2:  # 用气需求输入 + 1个支管
            self.delete_branch_button.config(state="disabled")
        
        # 立即保存到内存中
        if hasattr(self.parent, 'project_name') and hasattr(self.parent, 'project_code'):
            try:
                # 收集当前数据
                air_data = self.collect_data()
                
                # 获取当前项目ID
                project_name = self.parent.project_name.get()
                project_code = self.parent.project_code.get()
                current_project_id = f"{project_name}_{project_code}"
                
                # 保存到内存
                if hasattr(self.parent, 'saved_air_data'):
                    if not isinstance(self.parent.saved_air_data, dict):
                        self.parent.saved_air_data = {}
                    self.parent.saved_air_data[current_project_id] = air_data
                    print(f"删除支管后已更新内存数据: {current_project_id}, 剩余支管总数: {len(air_data.get('压缩空气支管数据', []))}")
            except Exception as e:
                print(f"删除支管后保存数据出错: {str(e)}")
    
    def close_window(self):
        """关闭窗口"""
        if hasattr(self, 'air_window') and self.air_window:
            self.on_window_close()
            
    def on_window_close(self):
        """处理窗口关闭事件，强制保存所有支管数据"""
        try:
            # 强制从标签页读取并保存支管数据
            branch_data = []
            
            # 如果标签控件存在
            if hasattr(self, 'tab_control') and self.tab_control:
                # 获取所有标签页（排除第一个用气需求标签页）
                all_tabs = self.tab_control.tabs()
                branch_tabs = all_tabs[1:] if len(all_tabs) > 1 else []
                
                print(f"检测到 {len(branch_tabs)} 个支管标签页")
                
                # 遍历每个标签页，强制读取数据
                for i, tab in enumerate(branch_tabs):
                    branch_index = i + 1  # 支管索引从1开始
                    
                    # 获取对应标签页文本
                    tab_text = self.tab_control.tab(tab, "text")
                    print(f"处理标签页: {tab_text}, 索引: {branch_index}")
                    
                    # 确保支管数据存在
                    if branch_index not in self.branch_data:
                        print(f"为标签页 {tab_text} 创建支管数据")
                        self.branch_data[branch_index] = {
                            "description": tk.StringVar(value=""),
                            "flow": tk.StringVar(value=""),
                            "pressure": tk.StringVar(value=""),
                            "calculated_diameter": tk.StringVar(value=""),
                            "selected_diameter": tk.StringVar(value=""),
                            "actual_velocity": tk.StringVar(value="")
                        }
                    
                    # 强制计算管径和流速
                    try:
                        self.calculate_branch_diameter(branch_index)
                        self.calculate_branch_actual_velocity(branch_index)
                    except Exception as e:
                        print(f"计算支管{branch_index}数据时出错: {str(e)}")
                    
                    # 读取并收集当前支管数据
                    branch = {
                        "支管编号": branch_index,
                        "支管描述": self.branch_data[branch_index]["description"].get(),
                        "流量": self.branch_data[branch_index]["flow"].get(),
                        "压力": self.branch_data[branch_index]["pressure"].get(),
                        "计算管径": self.branch_data[branch_index]["calculated_diameter"].get(),
                        "选取管径": self.branch_data[branch_index]["selected_diameter"].get(),
                        "实际流速": self.branch_data[branch_index]["actual_velocity"].get()
                    }
                    branch_data.append(branch)
                    print(f"已读取支管{branch_index}数据: {branch}")
            
            # 如果通过标签页方式找不到支管，尝试从branch_data中读取
            if not branch_data and self.branch_data:
                print("从branch_data字典读取支管数据")
                for idx in sorted(self.branch_data.keys()):
                    branch = {
                        "支管编号": idx,
                        "支管描述": self.branch_data[idx]["description"].get(),
                        "流量": self.branch_data[idx]["flow"].get(),
                        "压力": self.branch_data[idx]["pressure"].get(),
                        "计算管径": self.branch_data[idx]["calculated_diameter"].get(),
                        "选取管径": self.branch_data[idx]["selected_diameter"].get(),
                        "实际流速": self.branch_data[idx]["actual_velocity"].get()
                    }
                    branch_data.append(branch)
                    print(f"从字典读取支管{idx}数据: {branch}")
            
            print(f"总共收集到 {len(branch_data)} 个支管数据")
            
            # 收集主管道数据
            main_data = {
                "进车间压力(MPa)": self.air_pressure.get(),
                "设计流速(m/s)": self.air_velocity.get(),
                "连续用气总量(Nm³/h)": self.air_continuous_total.get(),
                "间歇用气总量(Nm³/h)": self.air_intermittent_total.get(),
                "总用气量(Nm³/h)": self.air_total_flow.get(),
                "计算管径(mm)": self.air_calculated_diameter.get(),
                "选取管径(mm)": self.air_standard_diameter.get(),
                "实际流速(m/s)": self.air_actual_velocity.get(),
                
                # 添加用气明细数据（带单位）
                "蓄热室吹扫用气(间歇Nm³/h)": self.air_input_vars["regenerator_purge"].get(),
                "喷枪冷却用气(连续Nm³/h)": self.air_input_vars["gun_cooling"].get(),
                "投料机用气(连续Nm³/h)": self.air_input_vars["feeder"].get(),
                "阀及工业电视用气(连续Nm³/h)": self.air_input_vars["valve_tv"].get(),
                "冷端机组用气(连续Nm³/h)": self.air_input_vars["cold_end"].get(),
                "退火窑及红外用气(连续Nm³/h)": self.air_input_vars["annealing_ir"].get(),
                "支通路加热用气(间歇Nm³/h)": self.air_input_vars["branch_heating"].get(),
                "压延机烧边火用气(连续Nm³/h)": self.air_input_vars["rolling_burn"].get(),
                "压延机清理用气(间歇Nm³/h)": self.air_input_vars["rolling_clean"].get(),
                
                # 保存支管数据
                "压缩空气支管数据": branch_data
            }
            
            # 添加单独的支管数据字段，确保支管数据可以独立访问
            for branch in branch_data:
                idx = branch["支管编号"]
                main_data[f"压缩空气支管{idx}描述"] = branch["支管描述"]
                main_data[f"压缩空气支管{idx}用气量(Nm³/h)"] = branch["流量"]
                main_data[f"压缩空气支管{idx}压力(MPa)"] = branch["压力"]
                main_data[f"压缩空气支管{idx}计算管径(mm)"] = branch["计算管径"]
                main_data[f"压缩空气支管{idx}选取管径(mm)"] = branch["选取管径"]
                main_data[f"压缩空气支管{idx}实际流速(m/s)"] = branch["实际流速"]
                main_data[f"压缩空气支管{idx}数据"] = branch
                print(f"添加独立支管字段: 压缩空气支管{idx}描述 = {branch['支管描述']}")
            
            # 保存数据到父级项目
            if self.parent and hasattr(self.parent, 'project_name') and hasattr(self.parent, 'project_code'):
                project_name = self.parent.project_name.get()
                project_code = self.parent.project_code.get()
                current_project_id = f"{project_name}_{project_code}"
                
                print(f"准备保存数据到项目: {current_project_id}")
                
                # 保存到父对象内存
                if hasattr(self.parent, 'saved_air_data'):
                    if not isinstance(self.parent.saved_air_data, dict):
                        self.parent.saved_air_data = {}
                    self.parent.saved_air_data[current_project_id] = main_data
                    print(f"数据已保存到内存: {current_project_id}")
                
                # 直接保存到历史文件
                if hasattr(self.parent, 'history_file') and self.parent.history_file:
                    try:
                        # 备份历史文件
                        if hasattr(self.parent, 'backup_history_file'):
                            self.parent.backup_history_file()
                        
                        # 读取历史文件
                        history = []
                        if os.path.exists(self.parent.history_file):
                            with open(self.parent.history_file, 'r', encoding='utf-8') as f:
                                history = json.load(f)
                        
                        # 更新或添加记录
                        found = False
                        for record in history:
                            if record.get("工程名称") == project_name and record.get("工程代号") == project_code:
                                # 保存原有非压缩空气数据
                                saved_data = {}
                                for key, value in record.items():
                                    if not (key.startswith("压缩空气") or 
                                           key in ["进车间压力", "设计流速", "连续用气总量", "间歇用气总量", 
                                                  "总用气量", "计算管径", "选取管径", "实际流速"] or
                                           key.startswith("蓄热室吹扫用气") or key.startswith("喷枪冷却用气") or
                                           key.startswith("投料机用气") or key.startswith("阀及工业电视用气") or
                                           key.startswith("冷端机组用气") or key.startswith("退火窑及红外用气") or
                                           key.startswith("支通路加热用气") or key.startswith("压延机烧边火用气") or
                                           key.startswith("压延机清理用气")):
                                        saved_data[key] = value
                                
                                # 清除所有压缩空气相关的键
                                for key in list(record.keys()):
                                    if (key.startswith("压缩空气") or 
                                        key in ["进车间压力", "设计流速", "连续用气总量", "间歇用气总量", 
                                              "总用气量", "计算管径", "选取管径", "实际流速"] or
                                        key.startswith("蓄热室吹扫用气") or key.startswith("喷枪冷却用气") or
                                        key.startswith("投料机用气") or key.startswith("阀及工业电视用气") or
                                        key.startswith("冷端机组用气") or key.startswith("退火窑及红外用气") or
                                        key.startswith("支通路加热用气") or key.startswith("压延机烧边火用气") or
                                        key.startswith("压延机清理用气")):
                                        del record[key]
                                
                                # 恢复原有非压缩空气数据
                                for key, value in saved_data.items():
                                    record[key] = value
                                
                                # 添加新的压缩空气数据
                                for key, value in main_data.items():
                                    record[key] = value
                                
                                # 更新时间
                                record["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                
                                found = True
                                print(f"更新现有记录: {project_name}_{project_code}")
                                break
                        
                        # 如果未找到，添加新记录
                        if not found:
                            new_record = {
                                "工程名称": project_name,
                                "工程代号": project_code,
                                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            # 添加所有压缩空气数据
                            for key, value in main_data.items():
                                new_record[key] = value
                            
                            history.append(new_record)
                            print(f"添加新记录: {project_name}_{project_code}")
                        
                        # 保存到历史文件
                        with open(self.parent.history_file, 'w', encoding='utf-8') as f:
                            json.dump(history, f, ensure_ascii=False, indent=2)
                        
                        print(f"成功保存到历史文件: {self.parent.history_file}")
                        
                    except Exception as e:
                        print(f"保存到历史文件时出错: {str(e)}")
                        traceback.print_exc()
            
            # 关闭窗口
            if hasattr(self, 'air_window') and self.air_window:
                self.air_window.destroy()
                self.air_window = None
                print("压缩空气窗口已关闭")
        except Exception as e:
            print(f"关闭压缩空气窗口时出错: {str(e)}")
            traceback.print_exc()
            # 尝试强制关闭窗口
            if hasattr(self, 'air_window') and self.air_window:
                try:
                    self.air_window.destroy()
                    self.air_window = None
                except:
                    pass
    def create_main_tab(self, tab):
        """创建用气需求输入选项卡"""
        # 创建输入区域框架
        input_frame_main = ttk.LabelFrame(tab, text="用气需求输入")
        input_frame_main.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建参数输入区域
        params_frame = ttk.Frame(input_frame_main)
        params_frame.pack(fill="x", padx=5, pady=5)
        
        # 添加压力和流速输入
        ttk.Label(params_frame, text="进车间压力(MPa):").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(params_frame, textvariable=self.air_pressure, width=15).grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(params_frame, text="设计流速(m/s):").grid(row=0, column=2, sticky="w", padx=5, pady=2)
        ttk.Entry(params_frame, textvariable=self.air_velocity, width=15).grid(row=0, column=3, sticky="w", padx=5, pady=2)
        
        # 创建用气需求输入区域
        usage_frame = ttk.Frame(input_frame_main)
        usage_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 定义用气需求项及其对应的变量键名
        demand_items = [
            ("蓄热室吹扫用气(间歇Nm³/h)", "regenerator_purge"),
            ("喷枪冷却用气(连续Nm³/h)", "gun_cooling"),
            ("投料机用气(连续Nm³/h)", "feeder"),
            ("阀及工业电视用气(连续Nm³/h)", "valve_tv"),
            ("冷端机组用气(连续Nm³/h)", "cold_end"),
            ("退火窑及红外用气(连续Nm³/h)", "annealing_ir"),
            ("支通路加热用气(间歇Nm³/h)", "branch_heating"),
            ("压延机烧边火用气(连续Nm³/h)", "rolling_burn"),
            ("压延机清理用气(间歇Nm³/h)", "rolling_clean")
        ]
        
        # 创建两列布局的输入字段
        column_count = 2
        row_idx = 0
        col_idx = 0
        
        for label, key in demand_items:
            # 创建标签和输入框 - 使用网格布局
            ttk.Label(usage_frame, text=label).grid(row=row_idx, column=col_idx*2, sticky="w", padx=5, pady=5)
            entry = ttk.Entry(usage_frame, textvariable=self.air_input_vars[key], width=15)
            entry.grid(row=row_idx, column=col_idx*2+1, sticky="w", padx=5, pady=5)
            
            # 更新列索引，如果达到列数限制，则转到下一行
            col_idx += 1
            if col_idx >= column_count:
                col_idx = 0
                row_idx += 1
        
        # 创建计算结果区域
        results_frame_main = ttk.LabelFrame(tab, text="总管计算")
        results_frame_main.pack(fill="x", padx=5, pady=5)
        
        # 添加计算结果字段
        ttk.Label(results_frame_main, text="连续用气总量(Nm³/h):").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(results_frame_main, textvariable=self.air_continuous_total, state="readonly", width=10).grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(results_frame_main, text="间歇用气总量(Nm³/h):").grid(row=0, column=2, sticky="w", padx=5, pady=2)
        ttk.Entry(results_frame_main, textvariable=self.air_intermittent_total, state="readonly", width=10).grid(row=0, column=3, sticky="w", padx=5, pady=2)
        
        ttk.Label(results_frame_main, text="总用气量(Nm³/h):").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(results_frame_main, textvariable=self.air_total_flow, state="readonly", width=10).grid(row=1, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(results_frame_main, text="总管管径(mm):").grid(row=1, column=2, sticky="w", padx=5, pady=2)
        ttk.Entry(results_frame_main, textvariable=self.air_calculated_diameter, state="readonly", width=10).grid(row=1, column=3, sticky="w", padx=5, pady=2)
        
        ttk.Label(results_frame_main, text="选取管径(mm):").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        diameter_values = ["", "15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400"]
        diameter_combobox = ttk.Combobox(results_frame_main, textvariable=self.air_standard_diameter, values=diameter_values, width=10)
        diameter_combobox.grid(row=2, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(results_frame_main, text="实际流速(m/s):").grid(row=2, column=2, sticky="w", padx=5, pady=2)
        ttk.Entry(results_frame_main, textvariable=self.air_actual_velocity, state="readonly", width=10).grid(row=2, column=3, sticky="w", padx=5, pady=2)
        
        # 添加按钮
        button_frame = ttk.Frame(tab)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(button_frame, text="计算", command=self.calculate_air_pipe).pack(side="right", padx=5)

    def create_branch_tab(self, parent_frame, branch_index):
        """
        创建压缩空气管标签页
        
        参数:
            parent_frame: 父框架
            branch_index: 支管索引
        """
        # 获取对应的变量
        branch_vars = self.branch_data[branch_index]
        
        # 创建输入区域框架
        input_frame = ttk.LabelFrame(parent_frame, text=f"压缩空气管{branch_index}计算参数")
        input_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建参数输入区域
        row = 0
        
        # 描述
        ttk.Label(input_frame, text="支管描述:").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=branch_vars["description"], width=40).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        row += 1
        # 用气总量
        ttk.Label(input_frame, text="用气总量(Nm³/h):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=branch_vars["flow"], width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        row += 1
        # 支管压力
        ttk.Label(input_frame, text="支管压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=branch_vars["pressure"], width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        # 创建计算结果区域
        result_frame = ttk.LabelFrame(parent_frame, text="计算结果")
        result_frame.pack(fill="x", padx=5, pady=5)
        
        # 添加计算结果字段
        row = 0
        ttk.Label(result_frame, text="计算管径(mm):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=branch_vars["calculated_diameter"], state="readonly", width=10).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        row += 1
        ttk.Label(result_frame, text="选取管径(mm):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        diameter_values = ["", "15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400"]
        ttk.Combobox(result_frame, textvariable=branch_vars["selected_diameter"], values=diameter_values, width=10).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        row += 1
        ttk.Label(result_frame, text="实际流速(m/s):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=branch_vars["actual_velocity"], state="readonly", width=10).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        # 添加计算按钮
        button_frame = ttk.Frame(parent_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(
            button_frame, 
            text="计算", 
            command=lambda: self.calculate_branch_diameter(branch_index)
        ).pack(side="right", padx=5)
        
        # 绑定输入字段变化事件
        branch_vars["flow"].trace_add("write", lambda *args: self.calculate_branch_diameter(branch_index))
        branch_vars["pressure"].trace_add("write", lambda *args: self.calculate_branch_diameter(branch_index))
        branch_vars["selected_diameter"].trace_add("write", lambda *args: self.calculate_branch_actual_velocity(branch_index))
    
    def setup_variable_trace(self):
        """设置变量跟踪以自动计算"""
        # 为用气明细变量添加跟踪
        for key, var in self.air_input_vars.items():
            var.trace_add("write", self.calculate_air_pipe)
            
        # 为总管变量添加跟踪
        self.air_pressure.trace_add("write", self.calculate_air_pipe)
        self.air_velocity.trace_add("write", self.calculate_air_pipe)
        self.air_standard_diameter.trace_add("write", self.calculate_air_actual_velocity)
        
        # 为支管变量添加跟踪
        for tab_index, data in self.branch_data.items():
            data["selected_diameter"].trace_add("write", lambda *args: self.calculate_air_branch_actual_velocity(tab_index))
    
    def calculate_air_pipe(self, *args):
        """计算压缩空气总管参数"""
        try:
            # 计算连续用气总量
            continuous_flow = 0
            continuous_items = ["gun_cooling", "feeder", "valve_tv", "cold_end", "annealing_ir", "rolling_burn"]
            for key in continuous_items:
                try:
                    value = float(self.air_input_vars[key].get() or 0)
                    continuous_flow += value
                except ValueError:
                    pass
            
            # 计算间歇用气总量
            intermittent_flow = 0
            intermittent_items = ["regenerator_purge", "branch_heating", "rolling_clean"]
            for key in intermittent_items:
                try:
                    value = float(self.air_input_vars[key].get() or 0)
                    intermittent_flow += value
                except ValueError:
                    pass
                    
            # 计算总用气量
            total_flow = continuous_flow + intermittent_flow
            
            # 更新显示
            self.air_continuous_total.set(f"{continuous_flow:.2f}")
            self.air_intermittent_total.set(f"{intermittent_flow:.2f}")
            self.air_total_flow.set(f"{total_flow:.2f}")
            
            # 如果有流量和速度，计算管径
            try:
                pressure = float(self.air_pressure.get() or 0)
                velocity = float(self.air_velocity.get() or 0)
                
                if total_flow > 0 and velocity > 0 and pressure > 0:
                    # 使用新公式：18.8*sqrt(总用气量*313/（2730*（进车间压力+0.1））/设计流速)
                    diameter = 18.8 * math.sqrt(total_flow * 313 / (2730 * (pressure + 0.1)) / velocity)
                    self.air_calculated_diameter.set(f"{diameter:.2f}")
                else:
                    self.air_calculated_diameter.set("")
            except ValueError:
                self.air_calculated_diameter.set("")
                
            # 计算实际流速
            self.calculate_air_actual_velocity()
            
        except Exception as e:
            print(f"计算压缩空气总管参数时出错: {str(e)}")
            traceback.print_exc()
    
    def calculate_air_actual_velocity(self, *args):
        """计算压缩空气总管实际流速"""
        try:
            total_flow = float(self.air_total_flow.get() or 0)
            selected_diameter = float(self.air_standard_diameter.get() or 0)
            pressure = float(self.air_pressure.get() or 0)
            
            if total_flow > 0 and selected_diameter > 0 and pressure > 0:
                # 实际流速 = 总用气量 * 313/2730 / (压力+0.1) * (18.8/选取管径)²
                actual_velocity = total_flow * 313 / 2730 / (pressure + 0.1) / (selected_diameter/18.8) / (selected_diameter/18.8)

                self.air_actual_velocity.set(f"{actual_velocity:.2f}")
            else:
                self.air_actual_velocity.set("")
        except ValueError:
            self.air_actual_velocity.set("")
        except Exception as e:
            print(f"计算压缩空气总管实际流速时出错: {str(e)}")
    
    def calculate_branch_diameter(self, branch_index):
        """计算压缩空气支管管径"""
        try:
            # 获取支管数据
            branch_vars = self.branch_data[branch_index]
            
            # 获取输入数据
            flow = float(branch_vars["flow"].get() or 0)
            pressure = float(branch_vars["pressure"].get() or 0)
            
            # 如果流量和压力有效，计算管径
            if flow > 0 and pressure > 0:
                # 使用主管相同速度作为设计流速
                velocity = float(self.air_velocity.get() or 20)  # 默认为20 m/s
                
                # 18.8 * sqrt(支管用气总量*313/2730/(支管进车间压力+0.1)/设计流速)
                diameter = 18.8 * math.sqrt(flow * 313 / (2730 * (pressure + 0.1)) / velocity)
                branch_vars["calculated_diameter"].set(f"{diameter:.2f}")
            else:
                branch_vars["calculated_diameter"].set("")
                
            # 计算实际流速
            self.calculate_branch_actual_velocity(branch_index)
                
        except ValueError:
            branch_vars["calculated_diameter"].set("")
        except Exception as e:
            print(f"计算压缩空气支管{branch_index}管径时出错: {str(e)}")
    
    def calculate_branch_actual_velocity(self, branch_index):
        """计算压缩空气支管实际流速"""
        try:
            # 获取支管数据
            branch_vars = self.branch_data[branch_index]
            
            # 获取输入数据
            flow = float(branch_vars["flow"].get() or 0)
            pressure = float(branch_vars["pressure"].get() or 0)
            selected_diameter = float(branch_vars["selected_diameter"].get() or 0)
            
            # 如果流量、压力和选取管径有效，计算实际流速
            if flow > 0 and pressure > 0 and selected_diameter > 0:
                #实际流速 = 总用气量 * 313/2730 / (压力+0.1) * (18.8/选取管径)²
                actual_velocity = flow * 313 / 2730 / (pressure + 0.1) / (selected_diameter/18.8) / (selected_diameter/18.8)

                branch_vars["actual_velocity"].set(f"{actual_velocity:.2f}")
            else:
                branch_vars["actual_velocity"].set("")
                
        except ValueError:
            branch_vars["actual_velocity"].set("")
        except Exception as e:
            print(f"计算压缩空气支管{branch_index}实际流速时出错: {str(e)}")
    
    def center_window(self, window):
        """将窗口居中显示"""
        # 确保几何管理器已更新
        window.update_idletasks()
        
        # 获取窗口尺寸
        width = window.winfo_width()
        height = window.winfo_height()
        
        # 如果窗口尺寸为1，可能还没有完全初始化
        if width <= 1:
            width = 800  # 使用默认宽度
        if height <= 1:
            height = 500  # 使用默认高度
            
        # 计算居中位置
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        
        # 设置窗口位置
        window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    
    # 在compressed_air.py文件中找到save_data方法，修改如下
    def save_data(self, project_data, record_id=None):
        """保存压缩空气计算数据到现有项目记录中"""
        try:
            # 从父对象获取项目名称和工程代号
            if hasattr(self.parent, 'project_name') and hasattr(self.parent, 'project_code'):
                project_name = self.parent.project_name.get()
                project_code = self.parent.project_code.get()
                
                # 防止保存空项目
                if not project_name and not project_code:
                    print("项目名称和工程代号均为空，取消保存压缩空气数据")
                    return
                
                # 使用父对象的备份方法
                if hasattr(self.parent, 'backup_history_file'):
                    self.parent.backup_history_file()
                
                # 收集压缩空气数据
                air_data = self.collect_data()
                if not air_data:
                    print("没有有效的压缩空气数据，取消保存")
                    return
                
                # 存储当前项目标识
                current_project_id = f"{project_name}_{project_code}"
                    
                # 【修改】同步数据到主程序，并关联到当前项目
                if hasattr(self.parent, 'saved_air_data'):
                    # 如果saved_air_data不是字典的字典，初始化为字典的字典
                    if not isinstance(self.parent.saved_air_data, dict) or \
                       not any(isinstance(v, dict) for v in self.parent.saved_air_data.values()):
                        self.parent.saved_air_data = {}
                    
                    # 以项目标识为键，存储压缩空气数据
                    self.parent.saved_air_data[current_project_id] = air_data
                    print(f"已将压缩空气数据同步到主程序，关联到项目: {current_project_id}")
                    
                    # 【修改】同步基本参数到主程序直接持有的变量，但不覆盖其他项目的数据
                    if hasattr(self.parent, 'air_pressure'):
                        self.parent.air_pressure.set(self.air_pressure.get())
                    if hasattr(self.parent, 'air_velocity'):
                        self.parent.air_velocity.set(self.air_velocity.get())
                    if hasattr(self.parent, 'air_continuous_total'):
                        self.parent.air_continuous_total.set(self.air_continuous_total.get())
                    if hasattr(self.parent, 'air_intermittent_total'):
                        self.parent.air_intermittent_total.set(self.air_intermittent_total.get())
                    if hasattr(self.parent, 'air_total_flow'):
                        self.parent.air_total_flow.set(self.air_total_flow.get())
                    if hasattr(self.parent, 'air_calculated_diameter'):
                        self.parent.air_calculated_diameter.set(self.air_calculated_diameter.get())
                    if hasattr(self.parent, 'air_standard_diameter'):
                        self.parent.air_standard_diameter.set(self.air_standard_diameter.get())
                    if hasattr(self.parent, 'air_actual_velocity'):
                        self.parent.air_actual_velocity.set(self.air_actual_velocity.get())
                    
                    # 【修改】同步用气明细到主程序变量
                    if hasattr(self.parent, 'air_input_vars'):
                        for key, var in self.air_input_vars.items():
                            if key in self.parent.air_input_vars:
                                self.parent.air_input_vars[key].set(var.get())
                
                # 读取现有历史记录
                history = []
                if os.path.exists(self.history_file):
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                
                # 在现有项目记录中查找匹配项目
                found = False
                found_index = -1
                for i, record in enumerate(history):
                    if (record.get("工程名称") == project_name and 
                        record.get("工程代号") == project_code):
                        found = True
                        found_index = i
                        print(f"找到匹配的项目记录: {project_name}, {project_code}")
                        break
                
                if found:
                    # 先清除可能已存在的压缩空气数据字段(扁平化的单独字段)
                    keys_to_remove = []
                    for key in history[found_index].keys():
                        if key.startswith("压缩空气") or key == "压缩空气支管数据":
                            keys_to_remove.append(key)
                    
                    for key in keys_to_remove:
                        history[found_index].pop(key, None)
                    
                    # 将压缩空气数据集成到现有项目记录中
                    for key, value in air_data.items():
                        history[found_index][key] = value
                    
                    # 确保项目数据保持一致性
                    history[found_index]["工程名称"] = project_name
                    history[found_index]["工程代号"] = project_code
                    history[found_index]["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 如果计算类型不是压缩空气管道计算，则保存原计算类型
                    if "计算类型" in history[found_index] and history[found_index]["计算类型"] != "压缩空气管道计算":
                        original_type = history[found_index]["计算类型"]
                        print(f"保留原有计算类型: {original_type}")
                    else:
                        history[found_index]["计算类型"] = "压缩空气管道计算"
                    
                    print(f"已将压缩空气数据集成到项目中: {project_name}, {project_code}")
                else:
                    # 如果没有找到匹配项目，创建新记录
                    project_data = air_data.copy()
                    project_data["工程名称"] = project_name
                    project_data["工程代号"] = project_code
                    project_data["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    project_data["计算类型"] = "压缩空气管道计算"
                    history.append(project_data)
                    print(f"创建新项目记录: {project_name}, {project_code}")
                
                # 保存更新后的历史记录
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump(history, f, ensure_ascii=False, indent=2)
                
                # 更新父对象的历史记录显示
                if hasattr(self.parent, 'update_history_display'):
                    self.parent.update_history_display()
                
                return True
            else:
                print("父对象缺少必要的项目信息属性")
                return False
        except Exception as e:
            print(f"保存压缩空气计算数据出错: {str(e)}")
            traceback.print_exc()
            return False
    def save_data_with_message(self):
        """保存数据并显示消息"""
        if self.save_data(None):
            messagebox.showinfo("保存成功", "压缩空气计算数据已保存")
        else:
            messagebox.showerror("保存失败", "保存压缩空气计算数据时出错")
    
    def collect_data(self):
        """收集压缩空气计算数据"""
        # 先计算一次，确保所有值都是最新的
        try:
            self.calculate_air_pipe()
        except Exception as e:
            print(f"自动计算压缩空气参数时出错: {str(e)}")
        
        # 打印支管数据情况
        print(f"收集数据前，共有 {len(self.branch_data)} 个支管")
        print(f"支管索引: {sorted(self.branch_data.keys())}")
        
        # 收集支管数据
        branch_data = self._collect_branch_data()
        
        # 添加单独的支管数据键
        branch_entries = {}
        for branch in branch_data:
            idx = branch["支管编号"]
            # 创建单独的键值对
            branch_entries[f"压缩空气支管{idx}描述"] = branch["支管描述"]
            branch_entries[f"压缩空气支管{idx}用气量(Nm³/h)"] = branch["流量"]
            branch_entries[f"压缩空气支管{idx}压力(MPa)"] = branch["压力"]
            branch_entries[f"压缩空气支管{idx}计算管径(mm)"] = branch["计算管径"]
            branch_entries[f"压缩空气支管{idx}选取管径(mm)"] = branch["选取管径"]
            branch_entries[f"压缩空气支管{idx}实际流速(m/s)"] = branch["实际流速"]
            branch_entries[f"压缩空气支管{idx}数据"] = branch
        
        data = {
            # 使用标准化的键名格式，确保保存的数据包含单位
            "进车间压力(MPa)": self.air_pressure.get(),
            "设计流速(m/s)": self.air_velocity.get(),
            "连续用气总量(Nm³/h)": self.air_continuous_total.get(),
            "间歇用气总量(Nm³/h)": self.air_intermittent_total.get(),
            "总用气量(Nm³/h)": self.air_total_flow.get(),
            "计算管径(mm)": self.air_calculated_diameter.get(),
            "选取管径(mm)": self.air_standard_diameter.get(),
            "实际流速(m/s)": self.air_actual_velocity.get(),
            
            # 添加用气明细数据（带单位）
            "蓄热室吹扫用气(间歇Nm³/h)": self.air_input_vars["regenerator_purge"].get(),
            "喷枪冷却用气(连续Nm³/h)": self.air_input_vars["gun_cooling"].get(),
            "投料机用气(连续Nm³/h)": self.air_input_vars["feeder"].get(),
            "阀及工业电视用气(连续Nm³/h)": self.air_input_vars["valve_tv"].get(),
            "冷端机组用气(连续Nm³/h)": self.air_input_vars["cold_end"].get(),
            "退火窑及红外用气(连续Nm³/h)": self.air_input_vars["annealing_ir"].get(),
            "支通路加热用气(间歇Nm³/h)": self.air_input_vars["branch_heating"].get(),
            "压延机烧边火用气(连续Nm³/h)": self.air_input_vars["rolling_burn"].get(),
            "压延机清理用气(间歇Nm³/h)": self.air_input_vars["rolling_clean"].get(),
            
            # 同时保存不带单位的键（兼容性考虑）
            "进车间压力": self.air_pressure.get(),
            "设计流速": self.air_velocity.get(),
            "连续用气总量": self.air_continuous_total.get(),
            "间歇用气总量": self.air_intermittent_total.get(),
            "总用气量": self.air_total_flow.get(),
            "计算管径": self.air_calculated_diameter.get(),
            "选取管径": self.air_standard_diameter.get(),
            "实际流速": self.air_actual_velocity.get(),
            "蓄热室吹扫用气": self.air_input_vars["regenerator_purge"].get(),
            "喷枪冷却用气": self.air_input_vars["gun_cooling"].get(),
            "投料机用气": self.air_input_vars["feeder"].get(),
            "阀及工业电视用气": self.air_input_vars["valve_tv"].get(),
            "冷端机组用气": self.air_input_vars["cold_end"].get(),
            "退火窑及红外用气": self.air_input_vars["annealing_ir"].get(),
            "支通路加热用气": self.air_input_vars["branch_heating"].get(),
            "压延机烧边火用气": self.air_input_vars["rolling_burn"].get(),
            "压延机清理用气": self.air_input_vars["rolling_clean"].get(),
            
            # 保存支管数据
            "压缩空气支管数据": branch_data
        }
        
        # 添加单独的支管数据字段
        for key, value in branch_entries.items():
            data[key] = value
        
        # 打印调试信息
        print("已收集压缩空气数据:")
        for key in ["进车间压力(MPa)", "设计流速(m/s)", "连续用气总量(Nm³/h)"]:
            print(f"  {key}: {data.get(key, '')}")
        print(f"  支管数据: {len(data.get('压缩空气支管数据', []))}个")
        for i, branch in enumerate(data.get('压缩空气支管数据', [])):
            print(f"  支管{i+1}: {branch.get('支管描述')} - 流量: {branch.get('流量')}")
        
        return data
    def load_data_from_record(self, record):
        """从历史记录中加载压缩空气数据"""
        try:
            # 加载基本数据
            self._load_basic_data(record)
            
            # 加载用气明细
            self._load_detail_data(record)
            
            # 加载支管数据
            self._load_branch_data(record)
            
            # 保存数据到内存
            self._data = record
            
            print("成功加载压缩空气计算数据")
            return True
        except Exception as e:
            print(f"加载压缩空气数据时出错: {str(e)}")
            traceback.print_exc()
            return False

    def _load_basic_data(self, record):
        """加载基本参数"""
        # 使用多种可能的键名尝试加载，提高兼容性
        if "进车间压力" in record:
            self.air_pressure.set(record["进车间压力"])
        elif "进车间压力(MPa)" in record:
            self.air_pressure.set(record["进车间压力(MPa)"])
            
        if "设计流速" in record:
            self.air_velocity.set(record["设计流速"])
        elif "设计流速(m/s)" in record:
            self.air_velocity.set(record["设计流速(m/s)"])
        
        # 加载各种流量数据
        if "连续用气总量" in record:
            self.air_continuous_total.set(record["连续用气总量"])
        elif "连续用气总量(Nm³/h)" in record:
            self.air_continuous_total.set(record["连续用气总量(Nm³/h)"])
            
        if "间歇用气总量" in record:
            self.air_intermittent_total.set(record["间歇用气总量"])
        elif "间歇用气总量(Nm³/h)" in record:
            self.air_intermittent_total.set(record["间歇用气总量(Nm³/h)"])
            
        if "总用气量" in record:
            self.air_total_flow.set(record["总用气量"])
        elif "总用气量(Nm³/h)" in record:
            self.air_total_flow.set(record["总用气量(Nm³/h)"])
            
        # 加载管径和流速数据
        if "计算管径" in record:
            self.air_calculated_diameter.set(record["计算管径"])
        elif "计算管径(mm)" in record:
            self.air_calculated_diameter.set(record["计算管径(mm)"])
            
        if "选取管径" in record:
            self.air_standard_diameter.set(record["选取管径"])
        elif "选取管径(mm)" in record:
            self.air_standard_diameter.set(record["选取管径(mm)"])
            
        if "实际流速" in record:
            self.air_actual_velocity.set(record["实际流速"])
        elif "实际流速(m/s)" in record:
            self.air_actual_velocity.set(record["实际流速(m/s)"])
        
        # 类似处理其他基本参数...
    def _create_branch_tab_for_loaded_data(self, idx):
        """为加载的数据创建支管标签页"""
        # 创建新标签页
        new_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(new_tab, text=f"压缩空气管{idx}")
        
        # 创建标签页的界面
        self.create_branch_tab(new_tab, idx)
        # 【新增】确保删除按钮状态正确
        if hasattr(self, 'delete_branch_button') and self.branch_tab_count > 1:
            self.delete_branch_button.config(state="normal")
        
    def _load_detail_data(self, record):
        """加载用气明细数据"""
        # 建立明确的键映射关系，确保不同格式的数据都能正确加载
        key_mappings = [
            # 标准格式带单位的键
            {"regenerator_purge": "蓄热室吹扫用气(间歇Nm³/h)"},
            {"gun_cooling": "喷枪冷却用气(连续Nm³/h)"},
            {"feeder": "投料机用气(连续Nm³/h)"},
            {"valve_tv": "阀及工业电视用气(连续Nm³/h)"},
            {"cold_end": "冷端机组用气(连续Nm³/h)"},
            {"annealing_ir": "退火窑及红外用气(连续Nm³/h)"},
            {"branch_heating": "支通路加热用气(间歇Nm³/h)"},
            {"rolling_burn": "压延机烧边火用气(连续Nm³/h)"},
            {"rolling_clean": "压延机清理用气(间歇Nm³/h)"},
            # 简化格式的键（没有单位）
            {"regenerator_purge": "蓄热室吹扫用气"},
            {"gun_cooling": "喷枪冷却用气"},
            {"feeder": "投料机用气"},
            {"valve_tv": "阀及工业电视用气"},
            {"cold_end": "冷端机组用气"},
            {"annealing_ir": "退火窑及红外用气"},
            {"branch_heating": "支通路加热用气"},
            {"rolling_burn": "压延机烧边火用气"},
            {"rolling_clean": "压延机清理用气"}
        ]
        
        # 遍历每个映射尝试加载
        for mapping in key_mappings:
            for var_key, record_key in mapping.items():
                if record_key in record and record[record_key]:
                    self.air_input_vars[var_key].set(record[record_key])
                    print(f"加载用气明细: {record_key} = {record[record_key]}")
    def _load_branch_data(self, record):
        """加载支管数据"""
        # 清除现有支管
        self.branch_data = {}
        self.branch_tab_count = 0
        
        # 如果已经打开了窗口，清除标签页
        if hasattr(self, 'tab_control') and self.tab_control:
            try:
                for tab in self.tab_control.tabs()[1:]:
                    self.tab_control.forget(tab)
            except Exception as e:
                print(f"清除标签页时出错: {str(e)}")
        
        # 打印调试信息
        print(f"加载支管数据: {'压缩空气支管数据' in record}")
        
        # 检查是否有支管数据列表
        branch_data = []
        if "压缩空气支管数据" in record and isinstance(record["压缩空气支管数据"], list):
            branch_data = record["压缩空气支管数据"]
            print(f"从压缩空气支管数据中找到 {len(branch_data)} 个支管")
        
        # 如果列表为空，尝试从单独的字段构建支管数据
        if not branch_data:
            print("尝试从单独字段加载支管数据")
            # 遍历可能的支管索引
            for i in range(1, 10):
                # 查找可能的描述键
                desc_found = False
                desc_keys = [
                    f"压缩空气支管{i}描述", 
                    f"压缩空气管{i}描述"
                ]
                
                for desc_key in desc_keys:
                    if desc_key in record and record[desc_key]:
                        desc_found = True
                        break
                
                if desc_found:
                    print(f"找到支管 {i} 描述键: {desc_key}")
                    
                    # 尝试不同格式的流量键
                    flow_value = ""
                    flow_keys = [
                        f"压缩空气支管{i}用气量(Nm³/h)",
                        f"压缩空气支管{i}流量",
                        f"压缩空气管{i}用气量(Nm³/h)",
                        f"压缩空气管{i}流量"
                    ]
                    for key in flow_keys:
                        if key in record and record[key]:
                            flow_value = record[key]
                            print(f"找到支管 {i} 流量键: {key}={flow_value}")
                            break
                    
                    # 尝试不同格式的压力键
                    pressure_value = ""
                    pressure_keys = [
                        f"压缩空气支管{i}压力(MPa)",
                        f"压缩空气支管{i}压力",
                        f"压缩空气管{i}压力(MPa)",
                        f"压缩空气管{i}压力"
                    ]
                    for key in pressure_keys:
                        if key in record and record[key]:
                            pressure_value = record[key]
                            print(f"找到支管 {i} 压力键: {key}={pressure_value}")
                            break
                    
                    # 尝试不同格式的计算管径键
                    calc_diam_value = ""
                    calc_diam_keys = [
                        f"压缩空气支管{i}计算管径(mm)",
                        f"压缩空气支管{i}计算管径",
                        f"压缩空气管{i}计算管径(mm)",
                        f"压缩空气管{i}计算管径"
                    ]
                    for key in calc_diam_keys:
                        if key in record and record[key]:
                            calc_diam_value = record[key]
                            print(f"找到支管 {i} 计算管径键: {key}={calc_diam_value}")
                            break
                    
                    # 尝试不同格式的选取管径键
                    sel_diam_value = ""
                    sel_diam_keys = [
                        f"压缩空气支管{i}选取管径(mm)",
                        f"压缩空气支管{i}选取管径",
                        f"压缩空气管{i}选取管径(mm)",
                        f"压缩空气管{i}选取管径"
                    ]
                    for key in sel_diam_keys:
                        if key in record and record[key]:
                            sel_diam_value = record[key]
                            print(f"找到支管 {i} 选取管径键: {key}={sel_diam_value}")
                            break
                    
                    # 尝试不同格式的实际流速键
                    vel_value = ""
                    vel_keys = [
                        f"压缩空气支管{i}实际流速(m/s)",
                        f"压缩空气支管{i}实际流速",
                        f"压缩空气管{i}实际流速(m/s)",
                        f"压缩空气管{i}实际流速"
                    ]
                    for key in vel_keys:
                        if key in record and record[key]:
                            vel_value = record[key]
                            print(f"找到支管 {i} 实际流速键: {key}={vel_value}")
                            break
                    
                    # 构建支管数据对象
                    branch = {
                        "支管编号": i,
                        "支管描述": record.get(desc_key, ""),
                        "流量": flow_value,
                        "压力": pressure_value,
                        "计算管径": calc_diam_value,
                        "选取管径": sel_diam_value,
                        "实际流速": vel_value
                    }
                    branch_data.append(branch)
                    print(f"添加支管数据: {branch}")
        
        print(f"最终加载了 {len(branch_data)} 个支管数据")
        
        # 加载所有找到的支管数据
        for branch in branch_data:
            idx = branch.get("支管编号", self.branch_tab_count + 1)
            self.branch_tab_count = max(self.branch_tab_count, idx)
            
            # 创建新支管变量
            self.branch_data[idx] = {
                "description": tk.StringVar(value=branch.get("支管描述", "")),
                "flow": tk.StringVar(value=branch.get("流量", "")),
                "pressure": tk.StringVar(value=branch.get("压力", "")),
                "calculated_diameter": tk.StringVar(value=branch.get("计算管径", "")),
                "selected_diameter": tk.StringVar(value=branch.get("选取管径", "")),
                "actual_velocity": tk.StringVar(value=branch.get("实际流速", ""))
            }
            
            # 如果窗口已打开，创建对应标签页
            if hasattr(self, 'tab_control') and self.tab_control:
                try:
                    self._create_branch_tab_for_loaded_data(idx)
                    print(f"为支管 {idx} 创建了标签页")
                except Exception as e:
                    print(f"为支管 {idx} 创建标签页出错: {str(e)}")
        
        # 如果没有找到任何支管数据，创建默认支管
        if not self.branch_data:
            print("没有找到支管数据，创建默认支管")
            self._create_first_branch_tab()
        
        # 确保窗口更新
        if hasattr(self, 'tab_control') and self.tab_control:
            self.tab_control.update()
        # 【新增】更新删除按钮状态
        if hasattr(self, 'delete_branch_button') and self.delete_branch_button:
            # 如果有多个支管标签页，启用删除按钮
            if self.branch_tab_count > 1:
                self.delete_branch_button.config(state="normal")
                print(f"启用删除按钮 - 支管数量: {self.branch_tab_count}")
            else:
                self.delete_branch_button.config(state="disabled")
                print(f"禁用删除按钮 - 支管数量: {self.branch_tab_count}")
    def _collect_branch_data(self):
        """收集所有支管数据"""
        branch_data = []
        print(f"开始收集支管数据，当前支管总数: {len(self.branch_data)}")
        
        # 确保所有标签页数据都被收集
        if hasattr(self, 'tab_control') and self.tab_control:
            # 获取标签页数量（除去主标签页）
            tab_count = len(self.tab_control.tabs()) - 1
            print(f"检测到 {tab_count} 个支管标签页")
        
        # 遍历所有branch_data中的支管
        for idx in sorted(self.branch_data.keys()):
            # 确保分支数据是有效的
            if idx not in self.branch_data or not isinstance(self.branch_data[idx], dict):
                print(f"跳过无效支管数据: 索引 {idx}")
                continue
            
            # 再次计算当前支管的管径和流速，确保数据最新
            try:
                self.calculate_branch_diameter(idx)
                self.calculate_branch_actual_velocity(idx)
            except Exception as e:
                print(f"计算支管{idx}数据时出错: {str(e)}")
            
            # 收集支管数据
            branch = {
                "支管编号": idx,
                "支管描述": self.branch_data[idx]["description"].get(),
                "流量": self.branch_data[idx]["flow"].get(),
                "压力": self.branch_data[idx]["pressure"].get(),
                "计算管径": self.branch_data[idx]["calculated_diameter"].get(),
                "选取管径": self.branch_data[idx]["selected_diameter"].get(),
                "实际流速": self.branch_data[idx]["actual_velocity"].get()
            }
            branch_data.append(branch)
            print(f"已收集支管{idx}数据: {branch['支管描述']} - 流量: {branch['流量']}")
        
        print(f"完成支管数据收集，共 {len(branch_data)} 个支管")
        return branch_data
    
    def load_data_from_current_project(self):
        """从当前项目加载数据"""
        try:
            # 如果parent存在，从parent中获取项目数据
            if hasattr(self.parent, 'current_data') and self.parent.current_data:
                project_data = self.parent.current_data
                
                # 直接检查项目数据中是否有压缩空气相关字段
                has_air_data = any(key.startswith("压缩空气") or key in ["进车间压力", "设计流速", "连续用气总量"] 
                                for key in project_data)
                
                if has_air_data:
                    print("在项目数据中找到压缩空气相关字段")
                    # 直接从项目数据加载
                    self.load_data_from_record(project_data)
                else:
                    # 尝试从压缩空气计算数据子对象加载
                    air_data = project_data.get("压缩空气计算数据", {})
                    if air_data:
                        self.load_data_from_record(air_data)
                    else:
                        # 如果没有压缩空气数据，创建一个默认的支管选项卡
                        self._create_first_branch_tab()
            else:
                # 如果没有项目数据，创建一个默认的支管选项卡
                self._create_first_branch_tab()
                
        except Exception as e:
            print(f"加载压缩空气数据时出错: {str(e)}")
            traceback.print_exc()
            # 如果加载出错，创建一个默认的支管选项卡
            self._create_first_branch_tab()
    def get_data(self):
        """获取当前计算器的数据（用于外部调用）"""
        return self.collect_data() 