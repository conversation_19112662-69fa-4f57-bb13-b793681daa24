import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import math
from datetime import datetime
import json
import os
import sys
import traceback

class OxygenPipeCalculator:
    def __init__(self, parent, parent_app):
        self.parent = parent
        self.app = parent_app  # 保存对主应用的引用以访问其方法和属性
        
        # 添加这一行以引用主应用的数据
        self.project_name = self.app.project_name
        self.project_code = self.app.project_code
        self.project_type = self.app.project_type
        self.furnace_count = self.app.furnace_count
        self.line_count = self.app.line_count
        self.history_file = self.app.history_file
        self.has_oxygen_lance = self.app.has_oxygen_lance
        self.is_oxygen_kiln = self.app.is_oxygen_kiln
    def show_window(self):
        """显示全氧窑氧气管道计算窗口"""
        # 如果窗口已经打开，则聚焦到该窗口
        if hasattr(self, 'oxygen_window') and self.oxygen_window.winfo_exists():
            self.oxygen_window.focus_set()
            return
        
        # 创建顶层窗口
        self.oxygen_window = tk.Toplevel(self.parent)
        self.oxygen_window.title("全氧窑氧气管道计算")
        self.oxygen_window.geometry("1050x600")
        
        # 设置窗口图标
        if hasattr(self.app, 'icon_path') and os.path.exists(self.app.icon_path):
            self.oxygen_window.iconbitmap(self.app.icon_path)
        
        # 创建主框架
        main_frame = ttk.Frame(self.oxygen_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建选项卡控件
        tab_control = ttk.Notebook(main_frame)
        
        # 创建三个选项卡
        tab1 = ttk.Frame(tab_control)
        tab2 = ttk.Frame(tab_control)
        tab3 = ttk.Frame(tab_control)
        
        tab_control.add(tab1, text="基本信息")
        tab_control.add(tab2, text="小炉区")
        tab_control.add(tab3, text="调节阀计算")
        
        tab_control.pack(fill="both", expand=True)
        
        # 初始化所需变量
        self.oxygen_flow = tk.StringVar(value="")             # 氧气正常流量
        self.oxygen_old_flow = tk.StringVar(value="")         # 窑老期流量
        self.oxygen_inlet_pressure = tk.StringVar(value="")   # 进车间压力
        self.oxygen_main_pre_pressure = tk.StringVar(value="") # 总管调节阀前压力
        self.oxygen_main_post_pressure = tk.StringVar(value="") # 总管调节阀后压力
        self.oxygen_branch_pre_pressure = tk.StringVar(value="") # 支管阀前压力
        self.oxygen_branch_post_pressure = tk.StringVar(value="") # 支管阀后压力
        self.oxygen_velocity = tk.StringVar(value="")         # 设计流速
        self.oxygen_temperature = tk.StringVar(value="")      # 设计温度
        self.oxygen_working_flow = tk.StringVar(value="")     # 工作状态下流量
        self.oxygen_working_old_flow = tk.StringVar(value="") # 窑老期工作状态下流量
        self.oxygen_main_diameter = tk.StringVar(value="")    # 进车间总管管径
        self.oxygen_selected_diameter = tk.StringVar(value="") # 选取管径
        self.oxygen_actual_velocity = tk.StringVar(value="")  # 反算流速
        # 添加以下变量声明
        self.oxygen_main_post_calc_diameter = tk.StringVar(value="")  # 总管调节阀后管径
        self.oxygen_main_post_selected_diameter = tk.StringVar(value="")  # 选取总管阀后管径
        self.oxygen_main_post_actual_velocity = tk.StringVar(value="")  # 反算总管阀后流速
        
        # 调节阀相关变量
        self.oxygen_main_c_large = tk.StringVar(value="")    # 总管C计大
        self.oxygen_main_c_small = tk.StringVar(value="")    # 总管C计小
        self.oxygen_main_c_selected = tk.StringVar(value="") # 总管C选定
        self.oxygen_main_k_large = tk.StringVar(value="")    # 总管K大
        self.oxygen_main_k_small = tk.StringVar(value="")    # 总管K小
        self.oxygen_density = tk.StringVar(value="1.43")         # 介质密度
        
        # 创建第一个选项卡内容 - 基本信息
        self.create_oxygen_basic_info_tab(tab1)
        
        # 创建第二个选项卡内容 - 小炉区
        self.create_oxygen_furnace_tab(tab2)
        
        # 创建第三个选项卡内容 - 调节阀计算
        self.create_oxygen_valve_tab(tab3)
        
        # 首先尝试从当前项目加载数据
        self.load_oxygen_data_from_current_project()
        
        # 添加窗口关闭事件处理
        def on_oxygen_window_close():
            try:
                # 标记窗口正在关闭
                self.oxygen_window._closing_silently = True
                
                # 收集所有数据而不仅仅是基本字段
                data = {
                    "工程名称": self.project_name.get(),
                    "工程代号": self.project_code.get(),
                    "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "计算类型": "全氧窑氧气管道计算",
                    "是否有0#氧枪": self.has_oxygen_lance.get(),
                    "是否是全氧窑": self.is_oxygen_kiln.get(),
                    "氧气正常流量(Nm³/h)": self.oxygen_flow.get(),
                    "窑老期流量(Nm³/h)": self.oxygen_old_flow.get(),
                    "氧气进车间压力(MPa)": self.oxygen_inlet_pressure.get(),
                    "氧气总管调节阀前压力(MPa)": self.oxygen_main_pre_pressure.get(),
                    "氧气总管调节阀后压力(MPa)": self.oxygen_main_post_pressure.get(),
                    "氧气支管阀前压力(MPa)": self.oxygen_branch_pre_pressure.get(),
                    "氧气支管阀后压力(MPa)": self.oxygen_branch_post_pressure.get(),
                    "氧气设计流速(m/s)": self.oxygen_velocity.get(),
                    "氧气设计温度(℃)": self.oxygen_temperature.get(),
                    "氧气工作状态下流量(Nm³/h)": self.oxygen_working_flow.get(),
                    "氧气窑老期工作状态下流量(Nm³/h)": self.oxygen_working_old_flow.get(),
                    "氧气进车间总管管径(mm)": self.oxygen_main_diameter.get(),
                    "氧气选取总管阀前管径(mm)": self.oxygen_selected_diameter.get(),
                    "氧气反算总管阀前流速(m/s)": self.oxygen_actual_velocity.get(),
                    "氧气总管调节阀后管径(mm)": self.oxygen_main_post_calc_diameter.get(),
                    "氧气选取总管阀后管径(mm)": self.oxygen_main_post_selected_diameter.get(),
                    "氧气反算总管阀后流速(m/s)": self.oxygen_main_post_actual_velocity.get(),
                    "氧气总管C计大": self.oxygen_main_c_large.get(),
                    "氧气总管C计小": self.oxygen_main_c_small.get(),
                    "氧气总管C选定": self.oxygen_main_c_selected.get(),
                    "氧气总管K大": self.oxygen_main_k_large.get(),
                    "氧气总管K小": self.oxygen_main_k_small.get(),
                    "氧气介质密度(kg/m³)": self.oxygen_density.get(),
                }
                
                # 收集小炉区数据
                furnace_data_list = []
                for idx, furnace in enumerate(self.oxygen_furnace_data):
                    furnace_data = {
                        "小炉编号": f"小炉{idx+1}",
                        "平均热负荷": furnace['heat_load'].get(),
                        "浮动值": furnace['float_value'].get(),
                        "阀前流量": furnace['pre_flow'].get(),
                        "阀前计算管径": furnace['pre_calc_diameter'].get(),
                        "阀前选取管径": furnace['pre_selected_diameter'].get(),
                        "阀前反算流速": furnace['pre_actual_velocity'].get(),
                        "阀后流量": furnace['post_flow'].get(),
                        "阀后计算管径": furnace['post_calc_diameter'].get(),
                        "阀后选取管径": furnace['post_selected_diameter'].get(),
                        "阀后反算流速": furnace['post_actual_velocity'].get(),
                        "C计大": furnace.get('c_large', tk.StringVar()).get(),
                        "C计小": furnace.get('c_small', tk.StringVar()).get(),
                        "C选定": furnace.get('c_selected', tk.StringVar()).get(),
                        "K大": furnace.get('k_large', tk.StringVar()).get(),
                        "K小": furnace.get('k_small', tk.StringVar()).get()
                    }
                    furnace_data_list.append(furnace_data)
                
                # 添加小炉区数据到主数据中
                data["氧气小炉区数据"] = furnace_data_list
                
                # 直接读取并更新history.json
                history_file = self.history_file
                history = []
                if os.path.exists(history_file):
                    try:
                        with open(history_file, 'r', encoding='utf-8') as f:
                            history = json.load(f)
                        print(f"读取历史记录文件成功，共{len(history)}条记录")
                    except Exception as e:
                        print(f"读取历史记录文件出错: {str(e)}")
                        history = []
                
                # 查找或创建记录
                found = False
                for record in history:
                    if record.get("工程名称") == data["工程名称"] and record.get("工程代号") == data["工程代号"]:
                        # 保存之前的压缩空气和自力式阀相关数据
                        air_data = {}
                        valve_data = {}
                        for key in list(record.keys()):
                            if ("压缩空气" in key or key.startswith("蓄热室吹扫用气") or 
                                key.startswith("喷枪冷却用气") or key.startswith("投料机用气") or 
                                key.startswith("阀及工业电视用气") or key.startswith("冷端机组用气") or 
                                key.startswith("退火窑及红外用气") or key.startswith("支通路加热用气") or 
                                key.startswith("压延机烧边火用气") or key.startswith("压延机清理用气") or 
                                key == "连续用气总量(Nm³/h)" or key == "间歇用气总量(Nm³/h)" or 
                                key == "总用气量(Nm³/h)" or key == "计算管径(mm)" or 
                                key == "选取管径(mm)" or key == "实际流速(m/s)" or 
                                key == "进车间压力(MPa)" or key == "设计流速(m/s)"):
                                air_data[key] = record[key]
                            elif key.startswith("自力式阀"):
                                valve_data[key] = record[key]
                        
                        # 更新记录
                        for key, value in data.items():
                            record[key] = value
                        
                        # 恢复保存的压缩空气和自力式阀数据
                        for key, value in air_data.items():
                            record[key] = value
                        for key, value in valve_data.items():
                            record[key] = value
                        
                        found = True
                        print(f"更新项目记录: {data['工程名称']}")
                        break
                
                if not found:
                    history.append(data)
                    print(f"创建新项目记录: {data['工程名称']}")
                
                # 保存更新后的历史记录
                try:
                    with open(history_file, 'w', encoding='utf-8') as f:
                        json.dump(history, f, ensure_ascii=False, indent=2)
                    print(f"历史记录保存成功，共{len(history)}条记录")
                except Exception as e:
                    print(f"保存历史记录文件出错: {str(e)}")
                    traceback.print_exc()
                
                # 执行原有的保存方法
                try:
                    self.save_data_without_closing(self.oxygen_window)
                    self.app.save_project(show_message=False)
                    print("全氧窑氧气管道窗口已关闭，数据已自动保存")
                except Exception as e:
                    print(f"调用原始保存方法失败: {str(e)}")
                    traceback.print_exc()
            except Exception as e:
                print(f"窗口关闭时自动保存数据失败: {str(e)}")
                traceback.print_exc()
            finally:
                # 无论如何都确保窗口关闭
                self.oxygen_window.destroy()
        
        # 绑定窗口关闭事件
        self.oxygen_window.protocol("WM_DELETE_WINDOW", on_oxygen_window_close)
        
        # 窗口居中显示
        self.oxygen_window.withdraw()
        self.app.center_window(self.oxygen_window)
        self.oxygen_window.deiconify()
    
    def create_oxygen_basic_info_tab(self, parent):
        """创建氧气管道基本信息选项卡"""
        # 创建输入框架
        input_frame = ttk.LabelFrame(parent, text="基本信息")
        input_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 使用网格布局创建输入字段
        row = 0
        
        # 第一行
        ttk.Label(input_frame, text="氧气正常流量(Nm³/h):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_flow, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="窑老期流量(Nm³/h):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_old_flow, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第二行
        row += 1
        ttk.Label(input_frame, text="进车间压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_inlet_pressure, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="总管调节阀前压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_main_pre_pressure, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第三行
        row += 1
        ttk.Label(input_frame, text="总管调节阀后压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_main_post_pressure, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="支管阀前压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_branch_pre_pressure, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第四行
        row += 1
        ttk.Label(input_frame, text="支管阀后压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_branch_post_pressure, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="设计流速(m/s):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_velocity, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第五行
        row += 1
        ttk.Label(input_frame, text="设计温度(℃):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.oxygen_temperature, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        # 创建结果显示框架
        result_frame = ttk.LabelFrame(parent, text="计算结果")
        result_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 结果显示 - 使用网格布局
        row = 0
        
        # 第一行结果
        ttk.Label(result_frame, text="工作状态下流量(Nm³/h):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=self.oxygen_working_flow, state="readonly", width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(result_frame, text="窑老期工作状态下流量(Nm³/h):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=self.oxygen_working_old_flow, state="readonly", width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第二行结果
        row += 1
        ttk.Label(result_frame, text="进车间总管管径(mm):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=self.oxygen_main_diameter, state="readonly", width=15, foreground="#0000FF").grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        # 修改标签文本
        ttk.Label(result_frame, text="选取总管阀前管径(mm):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        # 创建下拉选择框
        diameter_values = ["", "15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400"]
        ttk.Combobox(result_frame, textvariable=self.oxygen_selected_diameter, values=diameter_values, width=12).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第三行结果
        row += 1
        # 修改标签文本
        ttk.Label(result_frame, text="反算总管阀前流速(m/s):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=self.oxygen_actual_velocity, state="readonly", width=15, foreground="#0000FF").grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        # 添加新字段：总管调节阀后管径
        ttk.Label(result_frame, text="总管调节阀后管径(mm):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=self.oxygen_main_post_calc_diameter, state="readonly", width=15, foreground="#0000FF").grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第四行结果 - 新添加
        row += 1
        ttk.Label(result_frame, text="选取总管阀后管径(mm):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Combobox(result_frame, textvariable=self.oxygen_main_post_selected_diameter, values=diameter_values, width=12).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(result_frame, text="反算总管阀后流速(m/s):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(result_frame, textvariable=self.oxygen_main_post_actual_velocity, state="readonly", width=15, foreground="#0000FF").grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 添加变量跟踪，实现联动计算
        self.oxygen_flow.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_old_flow.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_inlet_pressure.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_main_pre_pressure.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_main_post_pressure.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_branch_pre_pressure.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_branch_post_pressure.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_velocity.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_temperature.trace_add("write", self.calculate_oxygen_params)
        self.oxygen_density.trace_add("write", self.calculate_oxygen_main_c_values)
        # 修改为:
        self.oxygen_selected_diameter.trace_add("write", self.calculate_oxygen_actual_velocity)
        # 添加对设备表管径的更新监听
        #self.oxygen_selected_diameter.trace_add("write", self.update_equipment_diameter)
        # 添加新的跟踪
        self.oxygen_main_post_selected_diameter.trace_add("write", self.calculate_oxygen_post_actual_velocity)


    def create_oxygen_furnace_tab(self, parent):
        """创建小炉区选项卡"""
        # 创建框架
        furnace_frame = ttk.Frame(parent)
        furnace_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建滚动区域
        canvas = tk.Canvas(furnace_frame)
        scrollbar = ttk.Scrollbar(furnace_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建表头
        headers = ["小炉编号", "平均热负荷", "浮动值", 
                "小炉阀前最大流量(Nm³/h)", "阀前计算管径", "阀前选取管径", "阀前反算流速",
                "小炉阀后最大流量(Nm³/h)", "阀后计算管径", "阀后选取管径", "阀后反算流速"]
        
        for col, header in enumerate(headers):
            ttk.Label(scrollable_frame, text=header, font=("宋体", 9, "")).grid(
                row=0, column=col, padx=5, pady=5, sticky="w"
            )
        
        # 为了与主界面小炉区联动，使用现有的小炉数据结构
        # 动态创建小炉行
        self.oxygen_furnace_data = []
        
        # 获取小炉数量，使用self.furnace_count（已在__init__中初始化）
        furnace_count = int(self.furnace_count.get() or 1)
        
        for i in range(furnace_count):
            furnace_row = {}
            row = i + 1  # 数据行从第2行开始（第1行是表头）
            
            # 小炉编号（只读）- 改为"小炉1"格式
            furnace_row['number'] = tk.StringVar(value=f"小炉{i+1}")
            ttk.Label(scrollable_frame, text=f"小炉{i+1}").grid(row=row, column=0, padx=5, pady=2)
            
            # 平均热负荷 - 与主界面联动
            furnace_row['heat_load'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['heat_load'], width=8).grid(row=row, column=1, padx=5, pady=2)
            
            # 浮动值 - 与主界面联动
            furnace_row['float_value'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['float_value'], width=8).grid(row=row, column=2, padx=5, pady=2)
            
            # 工作状态下小炉阀前流量
            furnace_row['pre_flow'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['pre_flow'], state="readonly", width=12).grid(row=row, column=3, padx=5, pady=2)
            
            # 阀前计算管径
            furnace_row['pre_calc_diameter'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['pre_calc_diameter'], state="readonly", width=8).grid(row=row, column=4, padx=5, pady=2)
            
            # 阀前选取管径
            furnace_row['pre_selected_diameter'] = tk.StringVar(value="")
            diameter_values = ["", "15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400"]
            ttk.Combobox(scrollable_frame, textvariable=furnace_row['pre_selected_diameter'], values=diameter_values, width=6).grid(row=row, column=5, padx=5, pady=2)
            
            # 阀前反算流速
            furnace_row['pre_actual_velocity'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['pre_actual_velocity'], state="readonly", width=8).grid(row=row, column=6, padx=5, pady=2)
            
            # 工作状态下小炉阀后流量
            furnace_row['post_flow'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['post_flow'], state="readonly", width=12).grid(row=row, column=7, padx=5, pady=2)
            
            # 阀后计算管径
            furnace_row['post_calc_diameter'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['post_calc_diameter'], state="readonly", width=8).grid(row=row, column=8, padx=5, pady=2)
            
            # 阀后选取管径
            furnace_row['post_selected_diameter'] = tk.StringVar(value="")
            ttk.Combobox(scrollable_frame, textvariable=furnace_row['post_selected_diameter'], values=diameter_values, width=6).grid(row=row, column=9, padx=5, pady=2)
            
            # 阀后反算流速
            furnace_row['post_actual_velocity'] = tk.StringVar(value="")
            ttk.Entry(scrollable_frame, textvariable=furnace_row['post_actual_velocity'], state="readonly", width=8).grid(row=row, column=10, padx=5, pady=2)
            
            # 添加变量跟踪
            furnace_row['heat_load'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_furnace(idx))
            furnace_row['float_value'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_furnace(idx))
            furnace_row['pre_selected_diameter'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_furnace_pre_velocity(idx))
            furnace_row['post_selected_diameter'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_furnace_post_velocity(idx))
            
            # 与主界面值同步 - 使用self.app访问主应用的数据
            if hasattr(self.app, 'furnace_data') and len(self.app.furnace_data) > i:
                # 从主界面同步热负荷和浮动值
                furnace_row['heat_load'].set(self.app.furnace_data[i]['heat_load'].get())
                furnace_row['float_value'].set(self.app.furnace_data[i]['float_value'].get())
                
                # 添加双向绑定，使两个界面的数据保持同步
                # 从这个界面到主界面的同步
                trace_id1 = furnace_row['heat_load'].trace_add("write", 
                    lambda *args, main_var=self.app.furnace_data[i]['heat_load'], 
                    oxy_var=furnace_row['heat_load']: 
                    self.sync_value_if_different(main_var, oxy_var.get()))
                    
                trace_id2 = furnace_row['float_value'].trace_add("write", 
                    lambda *args, main_var=self.app.furnace_data[i]['float_value'], 
                    oxy_var=furnace_row['float_value']: 
                    self.sync_value_if_different(main_var, oxy_var.get()))
                    
                # 存储trace id以便需要时移除
                furnace_row['heat_load_trace_id'] = trace_id1
                furnace_row['float_value_trace_id'] = trace_id2
                
                # 从主界面到这个界面的同步
                self.app.furnace_data[i]['heat_load'].trace_add("write", 
                    lambda *args, oxy_var=furnace_row['heat_load'], 
                    main_var=self.app.furnace_data[i]['heat_load']: 
                    self.sync_value_if_different(oxy_var, main_var.get()))
                    
                self.app.furnace_data[i]['float_value'].trace_add("write", 
                    lambda *args, oxy_var=furnace_row['float_value'], 
                    main_var=self.app.furnace_data[i]['float_value']: 
                    self.sync_value_if_different(oxy_var, main_var.get()))
            
            self.oxygen_furnace_data.append(furnace_row)
    # 5. 添加辅助方法，用于在不触发循环同步的情况下同步值
    def sync_value_if_different(self, target_var, new_value):
        """在不同时同步目标变量"""
        if target_var.get() != new_value:
            target_var.set(new_value)

    def create_oxygen_valve_tab(self, parent):
        """创建调节阀计算选项卡"""
        # 创建两个部分框架
        upper_frame = ttk.LabelFrame(parent, text="总管调节阀计算")
        upper_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        lower_frame = ttk.LabelFrame(parent, text="支管调节阀计算")
        lower_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 总管调节阀部分 - 改为4列布局
        # 第一行
        row = 0
        ttk.Label(upper_frame, text="总管调节阀前压力(MPa):").grid(row=row, column=0, sticky="w", padx=15, pady=5)
        ttk.Entry(upper_frame, textvariable=self.oxygen_main_pre_pressure, width=12).grid(row=row, column=1, sticky="w", padx=15, pady=5)
        
        ttk.Label(upper_frame, text="总管调节阀后压力(MPa):").grid(row=row, column=2, sticky="w", padx=15, pady=5)
        ttk.Entry(upper_frame, textvariable=self.oxygen_main_post_pressure, width=12).grid(row=row, column=3, sticky="w", padx=15, pady=5)
        
        # 第二行
        row += 1
        ttk.Label(upper_frame, text="介质密度(kg/cm³):").grid(row=row, column=0, sticky="w", padx=15, pady=5)
        ttk.Entry(upper_frame, textvariable=self.oxygen_density, width=12).grid(row=row, column=1, sticky="w", padx=15, pady=5)
        
        ttk.Label(upper_frame, text="总管C选定:").grid(row=row, column=2, sticky="w", padx=15, pady=5)
        ttk.Entry(upper_frame, textvariable=self.oxygen_main_c_selected, width=12).grid(row=row, column=3, sticky="w", padx=15, pady=5)
        
        
        # 第三行 - 修改字体为与其他字段一致的大小
        row += 1
        ttk.Label(upper_frame, text="总管C计大:").grid(row=row, column=0, sticky="w", padx=15, pady=5)
        ttk.Label(upper_frame, textvariable=self.oxygen_main_c_large, font=("宋体", 10, ""), foreground="#0000FF").grid(row=row, column=1, sticky="w", padx=15, pady=5)
        
        ttk.Label(upper_frame, text="总管C计小:").grid(row=row, column=2, sticky="w", padx=15, pady=5)
        ttk.Label(upper_frame, textvariable=self.oxygen_main_c_small, font=("宋体", 10, ""), foreground="#0000FF").grid(row=row, column=3, sticky="w", padx=15, pady=5)
        # 第四行 - 修改字体为与其他字段一致的大小
        row += 1
        ttk.Label(upper_frame, text="总管K大:").grid(row=row, column=0, sticky="w", padx=15, pady=5)
        ttk.Label(upper_frame, textvariable=self.oxygen_main_k_large, font=("宋体", 10, ""), foreground="#0000FF").grid(row=row, column=1, sticky="w", padx=15, pady=5)
        
        ttk.Label(upper_frame, text="总管K小:").grid(row=row, column=2, sticky="w", padx=15, pady=5)
        ttk.Label(upper_frame, textvariable=self.oxygen_main_k_small, font=("宋体", 10, ""), foreground="#0000FF").grid(row=row, column=3, sticky="w", padx=15, pady=5)
        
        # 第五行
        row += 1
        ttk.Label(upper_frame, text="支管阀前压力(MPa):").grid(row=row, column=0, sticky="w", padx=15, pady=5)
        ttk.Entry(upper_frame, textvariable=self.oxygen_branch_pre_pressure, width=12).grid(row=row, column=1, sticky="w", padx=15, pady=5)
        
        ttk.Label(upper_frame, text="支管阀后压力(MPa):").grid(row=row, column=2, sticky="w", padx=15, pady=5)
        ttk.Entry(upper_frame, textvariable=self.oxygen_branch_post_pressure, width=12).grid(row=row, column=3, sticky="w", padx=15, pady=5)
        
        # 支管调节阀部分 - 创建表格
        # 创建表头
        headers = ["小炉编号", "窑老期支管流量", "正常支管流量", "C计大", "C计小", "C选定", "K大", "K小"]
        
        for col, header in enumerate(headers):
            ttk.Label(lower_frame, text=header, font=("宋体", 9, "")).grid(
                row=0, column=col, padx=5, pady=5, sticky="w"
            )
        
        # 获取小炉数量
        furnace_count = int(self.furnace_count.get() or 1)
        # 动态创建小炉行数据结构 - 修改小炉编号格式并添加自动计算支管流量
        for i in range(furnace_count):
            row = i + 1  # 数据行从第2行开始（第1行是表头）
            
            # 小炉编号（只读）- 修改为"小炉1"格式
            ttk.Label(lower_frame, text=f"小炉{i+1}").grid(row=row, column=0, padx=5, pady=2)
            
            # 窑老期支管流量
            if 'old_branch_flow' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['old_branch_flow'] = tk.StringVar(value="")
            ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['old_branch_flow'], state="readonly", width=15).grid(row=row, column=1, padx=5, pady=2)
            
            # 正常支管流量
            if 'normal_branch_flow' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['normal_branch_flow'] = tk.StringVar(value="")
            ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['normal_branch_flow'], state="readonly", width=15).grid(row=row, column=2, padx=5, pady=2)
            
            # C计大
            if 'c_large' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['c_large'] = tk.StringVar(value="")
            ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['c_large'], width=10).grid(row=row, column=3, padx=5, pady=2)
            
            # C计小
            if 'c_small' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['c_small'] = tk.StringVar(value="")
            ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['c_small'], width=10).grid(row=row, column=4, padx=5, pady=2)
            
            # C选定
            if 'c_selected' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['c_selected'] = tk.StringVar(value="")
            ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['c_selected'], width=10).grid(row=row, column=5, padx=5, pady=2)
            
            # K大
            if 'k_large' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['k_large'] = tk.StringVar(value="")
            entry_k_large = ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['k_large'], state="readonly", width=10)
            entry_k_large.grid(row=row, column=6, padx=5, pady=2)
            
            # K小
            if 'k_small' not in self.oxygen_furnace_data[i]:
                self.oxygen_furnace_data[i]['k_small'] = tk.StringVar(value="")
            entry_k_small = ttk.Entry(lower_frame, textvariable=self.oxygen_furnace_data[i]['k_small'], state="readonly", width=10)
            entry_k_small.grid(row=row, column=7, padx=5, pady=2)
             # 为每个小炉添加变量跟踪 - 添加此部分代码
            # 监听C值变化以计算K值
            self.oxygen_furnace_data[i]['c_large'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_valve_k(idx))
            self.oxygen_furnace_data[i]['c_small'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_valve_k(idx))
            self.oxygen_furnace_data[i]['c_selected'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_valve_k(idx))
            
            
            
            # 添加支管压力变化监听
            self.oxygen_branch_pre_pressure.trace_add("write", lambda *args, idx=i: self.calculate_oxygen_valve_k(idx))
            self.oxygen_branch_post_pressure.trace_add("write", lambda *args, idx=i: self.calculate_oxygen_valve_k(idx))
            
            # 监听热负荷和浮动值变化以计算支管流量
            self.oxygen_furnace_data[i]['heat_load'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_branch_flow(idx))
            self.oxygen_furnace_data[i]['float_value'].trace_add("write", lambda *args, idx=i: self.calculate_oxygen_branch_flow(idx))
        
        # 添加对关键参数的跟踪，以触发总管C值的计算
        self.oxygen_main_pre_pressure.trace_add("write", self.calculate_oxygen_main_c_values)
        self.oxygen_main_post_pressure.trace_add("write", self.calculate_oxygen_main_c_values)
        self.oxygen_old_flow.trace_add("write", self.calculate_oxygen_main_c_values)
        self.oxygen_flow.trace_add("write", self.calculate_oxygen_main_c_values)
        self.oxygen_temperature.trace_add("write", self.calculate_oxygen_main_c_values)
        
        
        # 总管C选定值的变化只会影响K值的计算
        self.oxygen_main_c_selected.trace_add("write", self.calculate_oxygen_main_k_values)
        
        # 添加流量变量跟踪
        self.oxygen_flow.trace_add("write", self.calculate_oxygen_branch_flow_all)
        self.oxygen_old_flow.trace_add("write", self.calculate_oxygen_branch_flow_all)

        # 监听介质密度变化，触发所有支管的C值重新计算
        self.oxygen_density.trace_add("write", self.calculate_all_branch_valve_c)
    # 然后添加一个新方法来处理所有支管的重新计算
    def calculate_all_branch_valve_c(self, *args):
        """当介质密度变化时，重新计算所有支管的C值"""
        try:
            for i in range(len(self.oxygen_furnace_data)):
                self.calculate_oxygen_valve_k(i)
        except Exception as e:
            print(f"重新计算所有支管C值时出错: {str(e)}")
    # 添加新方法计算所有小炉的支管流量
    def calculate_oxygen_branch_flow_all(self, *args):
        """计算所有小炉的支管流量"""
        try:
            for i in range(len(self.oxygen_furnace_data)):
                self.calculate_oxygen_branch_flow(i)
        except Exception as e:
            print(f"计算小炉支管流量时出错: {str(e)}")
    def calculate_oxygen_main_k_values(self, *args):
        """仅计算总管K值，依赖于C值和C选定"""
        try:
            # 获取C选定值
            c_selected = float(self.oxygen_main_c_selected.get()) if self.oxygen_main_c_selected.get() else 0
            
            # 获取已计算的C值
            c_large = float(self.oxygen_main_c_large.get()) if self.oxygen_main_c_large.get() else 0
            c_small = float(self.oxygen_main_c_small.get()) if self.oxygen_main_c_small.get() else 0
            
            # 只有当C选定值和C值都有效时才计算K值
            if c_selected > 0 and c_large > 0 and c_small > 0:
                k_large = 1 + 0.68 * math.log10(c_large / c_selected)
                k_small = 1 + 0.68 * math.log10(c_small / c_selected)
                
                # 设置K值
                self.oxygen_main_k_large.set(f"{k_large:.2f}")
                self.oxygen_main_k_small.set(f"{k_small:.2f}")
            
        except ValueError as e:
            print(f"计算总管K值时输入数据无效: {str(e)}")
            
        except Exception as e:
            print(f"计算总管K值出错: {str(e)}")
            traceback.print_exc()
    # 添加新方法计算单个小炉的支管流量
    def calculate_oxygen_branch_flow(self, idx):
        """计算单个小炉的支管流量"""
        try:
            # 获取输入值
            heat_load = float(self.oxygen_furnace_data[idx]['heat_load'].get() or 0)
            float_value = float(self.oxygen_furnace_data[idx]['float_value'].get() or 0)
            normal_flow = float(self.oxygen_flow.get() or 0)
            old_flow = float(self.oxygen_old_flow.get() or 0)
            
            # 计算窑老期支管流量 = 窑老期流量*（该小炉平均热负荷+该小炉浮动值）
            if old_flow > 0 and (heat_load + float_value) > 0:
                old_branch_flow = old_flow * (heat_load + float_value) / 100
                self.oxygen_furnace_data[idx]['old_branch_flow'].set(f"{old_branch_flow:.2f}")
            else:
                self.oxygen_furnace_data[idx]['old_branch_flow'].set("")
                
            # 计算正常支管流量 = 氧气正常流量*该小炉平均热负荷
            if normal_flow > 0 and heat_load > 0:
                normal_branch_flow = normal_flow * heat_load / 100
                self.oxygen_furnace_data[idx]['normal_branch_flow'].set(f"{normal_branch_flow:.2f}")
            else:
                self.oxygen_furnace_data[idx]['normal_branch_flow'].set("")
                
            # 设置计算结果为蓝色
            # 需要在界面中找到对应的Entry控件并设置颜色
            # 由于这是只读的Entry控件，需要查找具体的小部件设置
            
        except Exception as e:
            print(f"计算小炉{idx+1}支管流量时出错: {str(e)}")

            
    def calculate_oxygen_params(self, *args):
        """计算氧气管道基本参数"""
        try:
            # 获取输入值
            normal_flow = float(self.oxygen_flow.get() or 0)
            old_flow = float(self.oxygen_old_flow.get() or 0)
            inlet_pressure = float(self.oxygen_inlet_pressure.get() or 0.1)
            main_pre_pressure = float(self.oxygen_main_pre_pressure.get() or 0.1)
            main_post_pressure = float(self.oxygen_main_post_pressure.get() or 0.1)
            temperature = float(self.oxygen_temperature.get() or 0)
            design_velocity = float(self.oxygen_velocity.get() or 1)
            
            # 计算工作状态下流量，按照新公式计算
            # 工作状态下流量=(氧气正常流量)*（273+设计温度）/2730/总管调节阀前压力
            if normal_flow > 0 and main_pre_pressure > 0:
                working_flow = normal_flow * (273 + temperature) / 2730 / main_pre_pressure
                self.oxygen_working_flow.set(f"{working_flow:.2f}")
            else:
                self.oxygen_working_flow.set("")
                
            # 窑老期工作状态下流量=(窑老期流量)*（273+设计温度）/2730/总管调节阀前压力
            if old_flow > 0 and main_pre_pressure > 0:
                working_old_flow = old_flow * (273 + temperature) / 2730 / main_pre_pressure
                self.oxygen_working_old_flow.set(f"{working_old_flow:.2f}")
            else:
                self.oxygen_working_old_flow.set("")
                
            # 计算进车间总管管径
            # 修正公式: 18.8*sqrt(窑老期流量*(273+设计温度)/2730/(总管调节阀前压力+0.1)/设计流速)
            if old_flow > 0 and main_pre_pressure > 0:
                main_diameter = 18.8 * math.sqrt(
                    old_flow * (273 + temperature) / 2730 / (main_pre_pressure+0.1) / design_velocity
                )
                self.oxygen_main_diameter.set(f"{main_diameter:.2f}")
            else:
                self.oxygen_main_diameter.set("")
            
            # 计算总管调节阀后管径
            # 修正公式: 18.8*sqrt(窑老期流量*(273+设计温度)/2730/(总管调节阀后压力+0.1)/设计流速)
            if old_flow > 0 and main_post_pressure > 0 and design_velocity > 0:
                post_calc_diameter = 18.8 * math.sqrt(
                    (old_flow * (273 + temperature) / 2730 / (main_post_pressure+0.1)) 
                    / design_velocity
                )
                self.oxygen_main_post_calc_diameter.set(f"{post_calc_diameter:.2f}")
            else:
                self.oxygen_main_post_calc_diameter.set("")
            # 计算反算流速
            self.calculate_oxygen_actual_velocity()
            
            # 计算总管阀后反算流速
            self.calculate_oxygen_post_actual_velocity()
            
            # 更新小炉区数据
            # 遍历所有的小炉计算数据
            for i in range(len(self.oxygen_furnace_data)):
                self.calculate_oxygen_furnace(i)
            # 在calculate_oxygen_params方法末尾添加
            # 设置计算结果显示为蓝色
            if hasattr(self, 'oxygen_main_diameter') and hasattr(self.oxygen_main_diameter, 'configure'):
                self.oxygen_main_diameter.configure(foreground="#0000FF")
            if hasattr(self, 'oxygen_main_post_calc_diameter') and hasattr(self.oxygen_main_post_calc_diameter, 'configure'):
                self.oxygen_main_post_calc_diameter.configure(foreground="#0000FF")
            if hasattr(self, 'oxygen_working_flow') and hasattr(self.oxygen_working_flow, 'configure'):
                self.oxygen_working_flow.configure(foreground="#0000FF")
            if hasattr(self, 'oxygen_working_old_flow') and hasattr(self.oxygen_working_old_flow, 'configure'):
                self.oxygen_working_old_flow.configure(foreground="#0000FF")
                
        except Exception as e:
            print(f"计算氧气管道参数时出错: {str(e)}")
    def calculate_oxygen_actual_velocity(self, *args):
        """计算总管阀前反算流速"""
        try:
            # 获取输入值
            old_flow = float(self.oxygen_old_flow.get() or 0)
            main_pre_pressure = float(self.oxygen_main_pre_pressure.get() or 0.1)
            temperature = float(self.oxygen_temperature.get() or 0)
            selected_diameter = float(self.oxygen_selected_diameter.get() or 0)
            
            # 只有当选择了管径时才计算反算流速
            # 修正公式: 反算总管阀前流速=窑老期流量*（273+设计温度）/2730/(总管调节阀前压力+0.1)/（选取总管阀前管径/18.8）/（选取总管阀前管径/18.8）
            if old_flow > 0 and main_pre_pressure > 0 and selected_diameter > 0:
                # 先计算窑老期工作状态下流量
                working_old_flow = old_flow * (273 + temperature) / 2730 / (main_pre_pressure+0.1)
                # 使用修正公式计算反算流速
                actual_velocity = working_old_flow / (selected_diameter / 18.8) / (selected_diameter / 18.8)
                self.oxygen_actual_velocity.set(f"{actual_velocity:.2f}")
                # 设置文字颜色为蓝色
                self.oxygen_actual_velocity.configure(foreground="#0000FF")
            else:
                self.oxygen_actual_velocity.set("")
                
        except Exception as e:
            print(f"计算总管阀前反算流速时出错: {str(e)}")

    def calculate_oxygen_post_actual_velocity(self, *args):
        """计算总管阀后反算流速"""
        try:
            # 获取输入值
            old_flow = float(self.oxygen_old_flow.get() or 0)
            main_post_pressure = float(self.oxygen_main_post_pressure.get() or 0.1)
            temperature = float(self.oxygen_temperature.get() or 0)
            selected_diameter = float(self.oxygen_main_post_selected_diameter.get() or 0)
            
            # 只有当选择了管径时才计算反算流速
            # 修正公式: 反算总管阀后流速=窑老期流量*（273+设计温度）/2730/(总管调节阀后压力+0.1)/（选取总管阀后管径/18.8）/（选取总管阀后管径/18.8）
            if old_flow > 0 and main_post_pressure > 0 and selected_diameter > 0:
                # 先计算窑老期工作状态下流量
                working_old_flow = old_flow * (273 + temperature) / 2730 / (main_post_pressure+0.1)
                # 使用修正公式计算反算流速
                actual_velocity = working_old_flow / (selected_diameter / 18.8) / (selected_diameter / 18.8)
                self.oxygen_main_post_actual_velocity.set(f"{actual_velocity:.2f}")
                # 设置文字颜色为蓝色
                self.oxygen_main_post_actual_velocity.configure(foreground="#0000FF")
            else:
                self.oxygen_main_post_actual_velocity.set("")
                
        except Exception as e:
            print(f"计算总管阀后反算流速时出错: {str(e)}")
    def calculate_oxygen_furnace(self, idx):
        """计算小炉区数据"""
        try:
            # 获取小炉输入值
            heat_load = float(self.oxygen_furnace_data[idx]['heat_load'].get() or 0)
            float_value = float(self.oxygen_furnace_data[idx]['float_value'].get() or 0)

            # 获取全局值
            old_flow = float(self.oxygen_old_flow.get() or 0)
            normal_flow = float(self.oxygen_flow.get() or 0)
            branch_pre_pressure = float(self.oxygen_branch_pre_pressure.get() or 0.1)
            branch_post_pressure = float(self.oxygen_branch_post_pressure.get() or 0.1)
            temperature = float(self.oxygen_temperature.get() or 0)
            design_velocity = float(self.oxygen_velocity.get() or 1)
            
            # 计算流量
            max_flow = old_flow * (heat_load + float_value) / 100 if heat_load + float_value > 0 else 0
            normal_flow_value = normal_flow * heat_load / 100 if heat_load > 0 else 0
            min_flow = normal_flow * (heat_load - float_value) / 100 if heat_load - float_value > 0 else 0
            
            # 更新阀前流量 - 添加蓝色显示
            self.oxygen_furnace_data[idx]['pre_flow'].set(f"{max_flow:.2f}")
            
            # 计算阀前管径
            if max_flow > 0 and branch_pre_pressure > 0 and design_velocity > 0:
                pre_calc_diameter = 18.8 * math.sqrt(
                    (max_flow * (273 + temperature) / 2730 / (branch_pre_pressure) )
                    / design_velocity
                )
                self.oxygen_furnace_data[idx]['pre_calc_diameter'].set(f"{pre_calc_diameter:.2f}")
            else:
                self.oxygen_furnace_data[idx]['pre_calc_diameter'].set("")
            
            # 更新阀后流量 - 添加蓝色显示
            self.oxygen_furnace_data[idx]['post_flow'].set(f"{max_flow:.2f}")
             # 计算阀后管径
            if max_flow > 0 and branch_post_pressure > 0 and design_velocity > 0:
                post_calc_diameter = 18.8 * math.sqrt(
                    (max_flow * (273 + temperature) / 2730 / (branch_post_pressure)) 
                    / design_velocity
                )
                self.oxygen_furnace_data[idx]['post_calc_diameter'].set(f"{post_calc_diameter:.2f}")
            else:
                self.oxygen_furnace_data[idx]['post_calc_diameter'].set("")
                
            # 计算反算流速
            self.calculate_oxygen_furnace_pre_velocity(idx)
            self.calculate_oxygen_furnace_post_velocity(idx)
            
            # 计算调节阀K值
            self.calculate_oxygen_valve_k(idx)
            
            # 修改小炉区所有计算结果为蓝色显示
            # 工作状态下小炉阀前流量
            if hasattr(self.oxygen_furnace_data[idx]['pre_flow'], 'configure'):
                self.oxygen_furnace_data[idx]['pre_flow'].configure(foreground="#0000FF")
            
            # 阀前计算管径显示为蓝色
            if hasattr(self.oxygen_furnace_data[idx]['pre_calc_diameter'], 'configure'):
                self.oxygen_furnace_data[idx]['pre_calc_diameter'].configure(foreground="#0000FF")
            
            # 工作状态下小炉阀后流量
            if hasattr(self.oxygen_furnace_data[idx]['post_flow'], 'configure'):
                self.oxygen_furnace_data[idx]['post_flow'].configure(foreground="#0000FF")
            
            # 阀后计算管径显示为蓝色
            if hasattr(self.oxygen_furnace_data[idx]['post_calc_diameter'], 'configure'):
                self.oxygen_furnace_data[idx]['post_calc_diameter'].configure(foreground="#0000FF")
            
        except Exception as e:
            print(f"计算小炉区数据时出错: {str(e)}")

    def calculate_oxygen_furnace_pre_velocity(self, idx):
        """计算小炉阀前反算流速"""
        try:
            # 获取输入值
            pre_flow = float(self.oxygen_furnace_data[idx]['pre_flow'].get() or 0)
            branch_pre_pressure = float(self.oxygen_branch_pre_pressure.get() or 0.1)
            temperature = float(self.oxygen_temperature.get() or 0)
            selected_diameter = float(self.oxygen_furnace_data[idx]['pre_selected_diameter'].get() or 0)
            
            # 只有当选择了管径时才计算反算流速
            # 反算流速=工作状态下小炉阀前流量/（阀前选取管径/18.8）/（阀前选取管径/18.8）
            if pre_flow > 0 and branch_pre_pressure > 0 and selected_diameter > 0:
                # 计算反算流速
                actual_velocity = pre_flow * (273 + temperature) / 2730 / branch_pre_pressure / (selected_diameter / 18.8) / (selected_diameter / 18.8)
                self.oxygen_furnace_data[idx]['pre_actual_velocity'].set(f"{actual_velocity:.2f}")
                
                # 设置显示颜色为蓝色
                entry = self.oxygen_furnace_data[idx]['pre_actual_velocity']
                widget_name = f"{scrollable_frame}.!entry{4+idx*10}"  # 根据实际小部件位置调整
                for widget in scrollable_frame.winfo_children():
                    if str(widget) == widget_name:
                        widget.config(foreground="#0000FF")
                        break
            else:
                self.oxygen_furnace_data[idx]['pre_actual_velocity'].set("")
                
        except Exception as e:
            print(f"计算小炉阀前反算流速时出错: {str(e)}")

    def calculate_oxygen_furnace_post_velocity(self, idx):
        """计算小炉阀后反算流速"""
        try:
            # 获取输入值
            post_flow = float(self.oxygen_furnace_data[idx]['post_flow'].get() or 0)
            branch_post_pressure = float(self.oxygen_branch_post_pressure.get() or 0.1)
            temperature = float(self.oxygen_temperature.get() or 0)
            selected_diameter = float(self.oxygen_furnace_data[idx]['post_selected_diameter'].get() or 0)
            
            # 只有当选择了管径时才计算反算流速
            # 使用正确的公式: 反算流速=(工作状态下小炉阀后流量)*(273+设计温度)/2730/分支管阀后压力/（阀后选取管径/18.8）/（阀后选取管径/18.8）
            if post_flow > 0 and branch_post_pressure > 0 and selected_diameter > 0:
                # 计算反算流速
                actual_velocity = post_flow * (273 + temperature) / 2730 / branch_post_pressure / (selected_diameter / 18.8) / (selected_diameter / 18.8)
                self.oxygen_furnace_data[idx]['post_actual_velocity'].set(f"{actual_velocity:.2f}")
            else:
                self.oxygen_furnace_data[idx]['post_actual_velocity'].set("")
                
        except Exception as e:
            print(f"计算小炉阀后反算流速时出错: {str(e)}")

    def calculate_oxygen_main_valve_k(self, *args):
        """计算总管调节阀参数"""
        try:
            # 获取输入参数
            old_flow = float(self.oxygen_old_flow.get())
            normal_flow = float(self.oxygen_flow.get())
            temperature = float(self.oxygen_temperature.get())
            pre_pressure = float(self.oxygen_main_pre_pressure.get())
            post_pressure = float(self.oxygen_main_post_pressure.get())
            
            # 计算压力差
            pressure_diff = pre_pressure - post_pressure
            if pressure_diff <= 0 or pre_pressure <= 0:
                messagebox.showerror("计算错误", "压力差必须大于0，且前压力必须大于0")
                return
            
            # 计算公式中的常数部分
            common_part = 1.167 * math.sqrt(0.743 * (273 + temperature)) / 514
            x_part = (1 - 0.46 * pressure_diff / pre_pressure)
            y_part = math.sqrt(pressure_diff * pre_pressure)
            
            # 计算C值
            c_large = common_part * old_flow / x_part / y_part
            c_small = common_part * normal_flow / x_part / y_part
            
            # 设置C值
            self.oxygen_main_c_large.set(f"{c_large:.2f}")
            self.oxygen_main_c_small.set(f"{c_small:.2f}")
            
            # 如果已设置C选定值，计算K值
            try:
                c_selected = float(self.oxygen_main_c_selected.get()) if self.oxygen_main_c_selected.get() else 0
                if c_selected > 0:
                    k_large = 1 + 0.68 * math.log10(c_large / c_selected)
                    k_small = 1 + 0.68 * math.log10(c_small / c_selected)
                    
                    # 设置K值
                    self.oxygen_main_k_large.set(f"{k_large:.2f}")
                    self.oxygen_main_k_small.set(f"{k_small:.2f}")
            except ValueError:
                # C选定值为空或无效，不计算K值
                pass
                
        except ValueError as e:
            print(f"输入值错误: {str(e)}")
            
        except Exception as e:
            print(f"计算总管调节阀K值出错: {str(e)}")
            traceback.print_exc()
    def calculate_oxygen_main_c_values(self, *args):
        """计算总管C值，不依赖于C选定"""
        try:
            # 获取输入参数
            old_flow = float(self.oxygen_old_flow.get())
            normal_flow = float(self.oxygen_flow.get())
            temperature = float(self.oxygen_temperature.get())
            pre_pressure = float(self.oxygen_main_pre_pressure.get())
            post_pressure = float(self.oxygen_main_post_pressure.get())
            
            # 计算压力差
            pressure_diff = pre_pressure - post_pressure
            if pressure_diff <= 0 or pre_pressure <= 0:
                messagebox.showerror("计算错误", "压力差必须大于0，且前压力必须大于0")
                return
            
            # 使用新公式计算C值
            # 总管C计大=1.167*窑老期流量*sqrt(介质密度*(273+设计温度))/(514*(1-0.46*(总管调节阀前压力*10-总管调节阀后压力*10)/(总管调节阀前压力*10+1))*sqrt((总管调节阀阀前压力*10-总管调节阀阀后压力*10)*(总管调节阀阀前压力*10+1))
            density = float(self.oxygen_density.get() or 1.43)  # 默认值仍为1.43
            pre_pressure_bar = pre_pressure * 10  # 转换为bar
            post_pressure_bar = post_pressure * 10 # 转换为bar
            pressure_diff_bar = pre_pressure_bar - post_pressure_bar
            
            c_large = 1.167 * old_flow * math.sqrt(density * (273 + temperature)) / (
                514 * (1 - 0.46 * pressure_diff_bar / (pre_pressure_bar + 1)) * 
                math.sqrt(pressure_diff_bar * (pre_pressure_bar + 1))
            )
            
            c_small = 1.167 * normal_flow * math.sqrt(density * (273 + temperature)) / (
                514 * (1 - 0.46 * pressure_diff_bar / (pre_pressure_bar + 1)) * 
                math.sqrt(pressure_diff_bar * (pre_pressure_bar + 1))
            )
            
            # 设置C值
            self.oxygen_main_c_large.set(f"{c_large:.2f}")
            self.oxygen_main_c_small.set(f"{c_small:.2f}")
            
        except ValueError as e:
            print(f"输入值错误: {str(e)}")
            
        except Exception as e:
            print(f"计算总管C值出错: {str(e)}")
            traceback.print_exc()
    def calculate_oxygen_valve_k(self, idx):
        """计算支管调节阀K值"""
        try:
            # 获取当前炉的数据
            furnace = self.oxygen_furnace_data[idx]
            
            # 获取输入参数
            old_branch_flow = float(furnace['old_branch_flow'].get() or 0)
            normal_branch_flow = float(furnace['normal_branch_flow'].get() or 0)
            temperature = float(self.oxygen_temperature.get() or 0)
            pre_pressure = float(self.oxygen_branch_pre_pressure.get() or 0)
            post_pressure = float(self.oxygen_branch_post_pressure.get() or 0)
            
            # 计算压力差
            pressure_diff = pre_pressure - post_pressure
            if pressure_diff <= 0 or pre_pressure <= 0:
                # 压力差或前压力不合理，但不弹窗提示，只记录日志
                print(f"小炉{idx+1}的压力差必须大于0，且前压力必须大于0")
                return
            
            # 使用新公式计算C值
            # 支管C计大=1.167*窑老期支管流量*sqrt(介质密度*(273+设计温度))/(514*(1-0.46*(支管调节阀前压力*10-支管调节阀后压力*10)/(支管调节阀前压力*10+1))*sqrt((支管调节阀阀前压力*10-支管调节阀阀后压力*10)*(支管调节阀阀前压力*10+1))
            density = float(self.oxygen_density.get() or 1.43)  # 默认值仍为1.43
            pre_pressure_bar = pre_pressure * 10  # 转换为bar
            post_pressure_bar = post_pressure * 10 # 转换为bar
            pressure_diff_bar = pre_pressure_bar - post_pressure_bar
            
            # 计算C值
            if old_branch_flow > 0 and normal_branch_flow > 0:
                c_large = 1.167 * old_branch_flow * math.sqrt(density * (273 + temperature)) / (
                    514 * (1 - 0.46 * pressure_diff_bar / (pre_pressure_bar + 1)) * 
                    math.sqrt(pressure_diff_bar * (pre_pressure_bar + 1))
                )
                
                c_small = 1.167 * normal_branch_flow * math.sqrt(density * (273 + temperature)) / (
                    514 * (1 - 0.46 * pressure_diff_bar / (pre_pressure_bar + 1)) * 
                    math.sqrt(pressure_diff_bar * (pre_pressure_bar + 1))
                )
                
                # 设置C值
                furnace['c_large'].set(f"{c_large:.2f}")
                furnace['c_small'].set(f"{c_small:.2f}")
                
                # 获取C选定值
                try:
                    c_selected = float(furnace['c_selected'].get()) if furnace['c_selected'].get() else 0
                    
                    # 如果已设置C选定值，计算K值
                    if c_selected > 0:
                        k_large = 1 + 0.68 * math.log10(c_large / c_selected)
                        k_small = 1 + 0.68 * math.log10(c_small / c_selected)
                        
                        # 设置K值
                        furnace['k_large'].set(f"{k_large:.2f}")
                        furnace['k_small'].set(f"{k_small:.2f}")
                except ValueError:
                    # C选定值为空或无效，不计算K值
                    pass
            
        except ValueError as e:
            print(f"小炉{idx+1}输入值错误: {str(e)}")
            
        except Exception as e:
            print(f"计算小炉{idx+1}调节阀K值出错: {str(e)}")
            traceback.print_exc()

    def calculate_oxygen_all(self):
        """计算所有氧气管道相关值"""
        try:
            # 计算基本参数
            self.calculate_oxygen_params()
            
            # 计算小炉区数据
            for i in range(len(self.oxygen_furnace_data)):
                self.calculate_oxygen_furnace(i)
            
            # 计算总管C值
            self.calculate_oxygen_main_c_values()
            
            # 计算总管K值
            self.calculate_oxygen_main_k_values()
            
            messagebox.showinfo("计算完成", "氧气管道计算已完成！")
            
        except Exception as e:
            print(f"计算过程中出错: {str(e)}")
    #6. 修改save_oxygen_data方法，确保保存新增字段
    def save_oxygen_data(self, window):
        """保存氧气管道计算数据到主项目"""
        try:
            # 备份历史文件
            self.backup_history_file()
             # 添加调试输出
            print(f"保存氧气数据时，是否有0#氧枪: {self.has_oxygen_lance.get()}, 是否是全氧窑: {self.is_oxygen_kiln.get()}")
        
            
            # 获取项目名称和工程代号
            project_name = self.project_name.get()
            project_code = self.project_code.get()
            
           
            
            # 读取现有历史记录
            history = []
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                    print(f"读取历史记录文件成功，共{len(history)}条记录")
                except Exception as e:
                    print(f"读取历史记录文件出错: {str(e)}")
                    # 文件可能损坏，使用空列表
                    history = []
            else:
                print(f"历史记录文件不存在，将创建新文件: {self.history_file}")
                
                # 检查历史文件目录是否存在
                history_dir = os.path.dirname(self.history_file)
                if not os.path.exists(history_dir):
                    os.makedirs(history_dir)
                    print(f"创建历史记录目录: {history_dir}")
            
            # 收集小炉区数据
            furnace_data_list = []
            for idx, furnace in enumerate(self.oxygen_furnace_data):
                furnace_data = {
                    "小炉编号": f"小炉{idx+1}",
                    "平均热负荷": furnace['heat_load'].get(),
                    "浮动值": furnace['float_value'].get(),
                    "阀前流量": furnace['pre_flow'].get(),
                    "阀前计算管径": furnace['pre_calc_diameter'].get(),
                    "阀前选取管径": furnace['pre_selected_diameter'].get(),
                    "阀前反算流速": furnace['pre_actual_velocity'].get(),
                    "阀后流量": furnace['post_flow'].get(),
                    "阀后计算管径": furnace['post_calc_diameter'].get(),
                    "阀后选取管径": furnace['post_selected_diameter'].get(),
                    "阀后反算流速": furnace['post_actual_velocity'].get(),
                    "C计大": furnace.get('c_large', tk.StringVar()).get(),
                    "C计小": furnace.get('c_small', tk.StringVar()).get(),
                    "C选定": furnace.get('c_selected', tk.StringVar()).get(),
                    "K大": furnace.get('k_large', tk.StringVar()).get(),
                    "K小": furnace.get('k_small', tk.StringVar()).get()
                }
                furnace_data_list.append(furnace_data)
            
            # 查找当前项目
            found = False
            for record in history:
                if (record.get("工程名称") == project_name and
                        record.get("工程代号") == project_code):
                    
                    # 保存之前的压缩空气和自力式阀相关数据
                    air_data = {}
                    valve_data = {}
                    for key in list(record.keys()):
                        if ("压缩空气" in key or key.startswith("蓄热室吹扫用气") or 
                            key.startswith("喷枪冷却用气") or key.startswith("投料机用气") or 
                            key.startswith("阀及工业电视用气") or key.startswith("冷端机组用气") or 
                            key.startswith("退火窑及红外用气") or key.startswith("支通路加热用气") or 
                            key.startswith("压延机烧边火用气") or key.startswith("压延机清理用气") or 
                            key == "连续用气总量(Nm³/h)" or key == "间歇用气总量(Nm³/h)" or 
                            key == "总用气量(Nm³/h)" or key == "计算管径(mm)" or 
                            key == "选取管径(mm)" or key == "实际流速(m/s)" or 
                            key == "进车间压力(MPa)" or key == "设计流速(m/s)"):
                            air_data[key] = record[key]
                        elif key.startswith("自力式阀"):
                            valve_data[key] = record[key]
                    
                    # 更新计算类型，但如果有其他类型也保留
                    if "计算类型" not in record or record.get("计算类型") != "压缩空气管道计算":
                        record["计算类型"] = "全氧窑氧气管道计算"
                    
                    # 更新基本信息数据
                    record["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    record["工程名称"] = project_name
                    record["工程代号"] = project_code
                    record["项目类型"] = self.project_type.get()
                    record["小炉数"] = self.furnace_count.get()
                    record["一窑几线"] = self.line_count.get()
                     # 添加这两个字段到顶层 - 这里是第一处修改
                    record["是否有0#氧枪"] = self.has_oxygen_lance.get()
                    record["是否是全氧窑"] = self.is_oxygen_kiln.get()
                    
                    # 添加氧气管道数据到顶层
                    record["氧气正常流量(Nm³/h)"] = self.oxygen_flow.get()
                    record["窑老期流量(Nm³/h)"] = self.oxygen_old_flow.get()
                    record["氧气进车间压力(MPa)"] = self.oxygen_inlet_pressure.get()
                    record["氧气总管调节阀前压力(MPa)"] = self.oxygen_main_pre_pressure.get()
                    record["氧气总管调节阀后压力(MPa)"] = self.oxygen_main_post_pressure.get()
                    record["氧气支管阀前压力(MPa)"] = self.oxygen_branch_pre_pressure.get()
                    record["氧气支管阀后压力(MPa)"] = self.oxygen_branch_post_pressure.get()
                    record["氧气设计流速(m/s)"] = self.oxygen_velocity.get()
                    record["氧气设计温度(℃)"] = self.oxygen_temperature.get()
                    record["氧气工作状态下流量(Nm³/h)"] = self.oxygen_working_flow.get()
                    record["氧气窑老期工作状态下流量(Nm³/h)"] = self.oxygen_working_old_flow.get()
                    record["氧气进车间总管管径(mm)"] = self.oxygen_main_diameter.get()
                    record["氧气选取总管阀前管径(mm)"] = self.oxygen_selected_diameter.get()
                    record["氧气反算总管阀前流速(m/s)"] = self.oxygen_actual_velocity.get()
                    record["氧气总管调节阀后管径(mm)"] = self.oxygen_main_post_calc_diameter.get()
                    record["氧气选取总管阀后管径(mm)"] = self.oxygen_main_post_selected_diameter.get()
                    record["氧气反算总管阀后流速(m/s)"] = self.oxygen_main_post_actual_velocity.get()
                    
                    # 调节阀计算数据
                    record["氧气总管C计大"] = self.oxygen_main_c_large.get()
                    record["氧气总管C计小"] = self.oxygen_main_c_small.get()
                    record["氧气总管C选定"] = self.oxygen_main_c_selected.get()
                    record["氧气总管K大"] = self.oxygen_main_k_large.get()
                    record["氧气总管K小"] = self.oxygen_main_k_small.get()
                    record["氧气介质密度(kg/m³)"] = self.oxygen_density.get()
                    
                    # 添加小炉区数据
                    record["氧气小炉区数据"] = furnace_data_list
                    
                    # 恢复保存的压缩空气和自力式阀数据
                    for key, value in air_data.items():
                        record[key] = value
                    for key, value in valve_data.items():
                        record[key] = value
                    
                    found = True
                    print(f"更新项目记录: {project_name}，保留压缩空气和自力式阀数据")
                    break
            
            if not found:
                # 如果没有找到当前项目，创建新记录
                new_record = {
                    "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "工程名称": project_name,
                    "工程代号": project_code,
                    "项目类型": self.project_type.get(),
                    "小炉数": self.furnace_count.get(),
                    "一窑几线": self.line_count.get(),
                    "计算类型": "全氧窑氧气管道计算",
                    # 添加这两个字段到顶层 - 这里是第二处修改
                    "是否有0#氧枪": self.has_oxygen_lance.get(),
                    "是否是全氧窑": self.is_oxygen_kiln.get(),
                    
                    # 基本信息添加到顶层
                    "氧气正常流量(Nm³/h)": self.oxygen_flow.get(),
                    "窑老期流量(Nm³/h)": self.oxygen_old_flow.get(),
                    "氧气进车间压力(MPa)": self.oxygen_inlet_pressure.get(),
                    "氧气总管调节阀前压力(MPa)": self.oxygen_main_pre_pressure.get(),
                    "氧气总管调节阀后压力(MPa)": self.oxygen_main_post_pressure.get(),
                    "氧气支管阀前压力(MPa)": self.oxygen_branch_pre_pressure.get(),
                    "氧气支管阀后压力(MPa)": self.oxygen_branch_post_pressure.get(),
                    "氧气设计流速(m/s)": self.oxygen_velocity.get(),
                    "氧气设计温度(℃)": self.oxygen_temperature.get(),
                    "氧气工作状态下流量(Nm³/h)": self.oxygen_working_flow.get(),
                    "氧气窑老期工作状态下流量(Nm³/h)": self.oxygen_working_old_flow.get(),
                    "氧气进车间总管管径(mm)": self.oxygen_main_diameter.get(),
                    "氧气选取总管阀前管径(mm)": self.oxygen_selected_diameter.get(),
                    "氧气反算总管阀前流速(m/s)": self.oxygen_actual_velocity.get(),
                    "氧气总管调节阀后管径(mm)": self.oxygen_main_post_calc_diameter.get(),
                    "氧气选取总管阀后管径(mm)": self.oxygen_main_post_selected_diameter.get(),
                    "氧气反算总管阀后流速(m/s)": self.oxygen_main_post_actual_velocity.get(),
                    
                    # 调节阀计算数据
                    "氧气总管C计大": self.oxygen_main_c_large.get(),
                    "氧气总管C计小": self.oxygen_main_c_small.get(),
                    "氧气总管C选定": self.oxygen_main_c_selected.get(),
                    "氧气总管K大": self.oxygen_main_k_large.get(),
                    "氧气总管K小": self.oxygen_main_k_small.get(),
                    "氧气介质密度(kg/m³)": self.oxygen_density.get(),
                    
                    # 小炉区数据
                    "氧气小炉区数据": furnace_data_list
                }
                history.append(new_record)
                print(f"创建新项目记录: {project_name}")
            
            # 保存更新后的历史记录
            try:
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    # 添加调试，确认最终写入的值
                    if found_index >= 0:
                        print(f"写入history.json前，是否有0#氧枪: {history[found_index].get('是否有0#氧枪')}, 是否是全氧窑: {history[found_index].get('是否是全氧窑')}")
                    else:
                        print(f"写入history.json前，是否有0#氧枪: {project_data.get('是否有0#氧枪')}, 是否是全氧窑: {project_data.get('是否是全氧窑')}")
                    json.dump(history, f, ensure_ascii=False, indent=2)
                print(f"历史记录保存成功，共{len(history)}条记录")
            except Exception as e:
                print(f"保存历史记录文件出错: {str(e)}")
                traceback.print_exc()
                raise e
            
            # 更新历史记录显示
            self.update_history_display()
            
            # 只在手动点击保存按钮时显示提示
            if window is not None and not hasattr(window, '_closing_silently'):
                print("氧气管道数据已保存")
                messagebox.showinfo("保存成功", "氧气管道计算数据已保存到项目中！")
                # 窗口关闭处理
                if window is not None:
                    window.destroy()
            else:
                print("氧气管道数据已静默保存")
            
        except Exception as e:
            error_msg = f"保存氧气管道数据时出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("保存失败", error_msg)
    def save_oxygen_project_to_history(self, oxygen_data):
        """修改此方法，确保氧气数据保存到主项目文件"""
        try:
            # 备份历史文件
            self.backup_history_file()
            
            # 读取当前历史记录
            history = []
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception as e:
                    print(f"读取历史记录出错: {str(e)}")
            
            # 获取项目名称和工程代号
            project_name = self.project_name.get()
            project_code = self.project_code.get()
            
            # 查找是否存在相同项目
            found_index = -1
            for i, record in enumerate(history):
                if record.get("工程名称") == project_name:
                    found_index = i
                    break
                if project_code and record.get("工程代号") == project_code:
                    found_index = i
                    break
            
            # 构建完整的项目数据
            if found_index >= 0:
                # 将氧气数据直接添加到顶层
                 # 添加这两个字段到顶层 - 这里是需要添加的代码
                history[found_index]["是否有0#氧枪"] = self.has_oxygen_lance.get()
                history[found_index]["是否是全氧窑"] = self.is_oxygen_kiln.get()
                
                # 基本信息
                history[found_index]["氧气正常流量(Nm³/h)"] = self.oxygen_flow.get()
                history[found_index]["窑老期流量(Nm³/h)"] = self.oxygen_old_flow.get()
                history[found_index]["氧气进车间压力(MPa)"] = self.oxygen_inlet_pressure.get()
                history[found_index]["氧气总管调节阀前压力(MPa)"] = self.oxygen_main_pre_pressure.get()
                history[found_index]["氧气总管调节阀后压力(MPa)"] = self.oxygen_main_post_pressure.get()
                history[found_index]["氧气支管阀前压力(MPa)"] = self.oxygen_branch_pre_pressure.get()
                history[found_index]["氧气支管阀后压力(MPa)"] = self.oxygen_branch_post_pressure.get()
                history[found_index]["氧气设计流速(m/s)"] = self.oxygen_velocity.get()
                history[found_index]["氧气设计温度(℃)"] = self.oxygen_temperature.get()
                history[found_index]["氧气工作状态下流量(Nm³/h)"] = self.oxygen_working_flow.get()
                history[found_index]["氧气窑老期工作状态下流量(Nm³/h)"] = self.oxygen_working_old_flow.get()
                history[found_index]["氧气进车间总管管径(mm)"] = self.oxygen_main_diameter.get()
                history[found_index]["氧气选取总管阀前管径(mm)"] = self.oxygen_selected_diameter.get()
                history[found_index]["氧气反算总管阀前流速(m/s)"] = self.oxygen_actual_velocity.get()
                history[found_index]["氧气总管调节阀后管径(mm)"] = self.oxygen_main_post_calc_diameter.get()
                history[found_index]["氧气选取总管阀后管径(mm)"] = self.oxygen_main_post_selected_diameter.get()
                history[found_index]["氧气反算总管阀后流速(m/s)"] = self.oxygen_main_post_actual_velocity.get()
                
                # 调节阀计算数据
                history[found_index]["氧气总管C计大"] = self.oxygen_main_c_large.get()
                history[found_index]["氧气总管C计小"] = self.oxygen_main_c_small.get()
                history[found_index]["氧气总管C选定"] = self.oxygen_main_c_selected.get()
                history[found_index]["氧气总管K大"] = self.oxygen_main_k_large.get()
                history[found_index]["氧气总管K小"] = self.oxygen_main_k_small.get()
                history[found_index]["氧气介质密度(kg/m³)"] = self.oxygen_density.get()
                
                # 小炉区数据
                history[found_index]["氧气小炉区数据"] = []
                for idx, furnace in enumerate(self.oxygen_furnace_data):
                    furnace_data = {
                        "小炉编号": f"小炉{idx+1}",
                        "平均热负荷": furnace['heat_load'].get(),
                        "浮动值": furnace['float_value'].get(),
                        "阀前流量": furnace['pre_flow'].get(),
                        "阀前计算管径": furnace['pre_calc_diameter'].get(),
                        "阀前选取管径": furnace['pre_selected_diameter'].get(),
                        "阀前反算流速": furnace['pre_actual_velocity'].get(),
                        "阀后流量": furnace['post_flow'].get(),
                        "阀后计算管径": furnace['post_calc_diameter'].get(),
                        "阀后选取管径": furnace['post_selected_diameter'].get(),
                        "阀后反算流速": furnace['post_actual_velocity'].get(),
                        "C计大": furnace.get('c_large', tk.StringVar()).get(),
                        "C计小": furnace.get('c_small', tk.StringVar()).get(),
                        "C选定": furnace.get('c_selected', tk.StringVar()).get(),
                        "K大": furnace.get('k_large', tk.StringVar()).get(),
                        "K小": furnace.get('k_small', tk.StringVar()).get()
                    }
                    history[found_index]["氧气小炉区数据"].append(furnace_data)
            
            # 保存历史记录
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=4)
            
            # 更新历史记录显示
            self.update_history_display()
                
        except Exception as e:
            print(f"将氧气管道数据保存到主项目时出错: {str(e)}")
            traceback.print_exc()

    def export_oxygen_data(self):
        """导出氧气管道计算数据到Excel"""
        try:
            # 弹出文件保存对话框
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel 文件", "*.xlsx")],
                title="导出氧气管道计算数据"
            )
            
            if not file_path:
                return
            
            # 创建Excel写入器
            writer = pd.ExcelWriter(file_path, engine='openpyxl')
            
            # 基本信息数据
            basic_info = [
                ["全氧窑氧气管道计算", ""],
                ["项目信息", ""],
                ["工程名称", self.project_name.get()],
                ["工程代号", self.project_code.get()],
                ["小炉数", self.furnace_count.get()],
                ["一窑几线", self.line_count.get()],
                ["", ""],
                ["基本参数", ""],
                ["氧气正常流量(Nm³/h)", self.oxygen_flow.get()],
                ["窑老期流量(Nm³/h)", self.oxygen_old_flow.get()],
                ["进车间压力(MPa)", self.oxygen_inlet_pressure.get()],
                ["总管调节阀前压力(MPa)", self.oxygen_main_pre_pressure.get()],
                ["总管调节阀后压力(MPa)", self.oxygen_main_post_pressure.get()],
                ["支管阀前压力(MPa)", self.oxygen_branch_pre_pressure.get()],
                ["支管阀后压力(MPa)", self.oxygen_branch_post_pressure.get()],
                ["设计流速(m/s)", self.oxygen_velocity.get()],
                ["设计温度(℃)", self.oxygen_temperature.get()],
                ["", ""],
                ["计算结果", ""],
                ["工作状态下流量(Nm³/h)", self.oxygen_working_flow.get()],
                ["窑老期工作状态下流量(Nm³/h)", self.oxygen_working_old_flow.get()],
                ["进车间总管管径(mm)", self.oxygen_main_diameter.get()],
                ["选取管径(mm)", self.oxygen_selected_diameter.get()],
                ["反算流速(m/s)", self.oxygen_actual_velocity.get()],
                ["", ""],
                ["总管调节阀计算", ""],
                ["总管C计大", self.oxygen_main_c_large.get()],
                ["总管C计小", self.oxygen_main_c_small.get()],
                ["总管C选定", self.oxygen_main_c_selected.get()],
                ["总管K大", self.oxygen_main_k_large.get()],
                ["总管K小", self.oxygen_main_k_small.get()],
                ["介质密度(kg/cm³)", self.oxygen_density.get()]
            ]
            
            # 转换为DataFrame并导出
            df_basic = pd.DataFrame(basic_info)
            df_basic.to_excel(writer, sheet_name='基本信息', index=False, header=False)
            
            # 小炉区数据
            furnace_data = []
            
            # 添加表头
            headers = ["小炉编号", "平均热负荷", "浮动值", 
                    "阀前流量(Nm³/h)", "阀前计算管径", "阀前选取管径", "阀前反算流速",
                    "阀后流量(Nm³/h)", "阀后计算管径", "阀后选取管径", "阀后反算流速"]
            furnace_data.append(headers)
            
            # 添加小炉数据
            for i, furnace in enumerate(self.oxygen_furnace_data):
                row = [
                    i + 1,
                    furnace['heat_load'].get(),
                    furnace['float_value'].get(),
                    furnace['pre_flow'].get(),
                    furnace['pre_calc_diameter'].get(),
                    furnace['pre_selected_diameter'].get(),
                    furnace['pre_actual_velocity'].get(),
                    furnace['post_flow'].get(),
                    furnace['post_calc_diameter'].get(),
                    furnace['post_selected_diameter'].get(),
                    furnace['post_actual_velocity'].get()
                ]
                furnace_data.append(row)
            
            # 转换为DataFrame并导出
            df_furnace = pd.DataFrame(furnace_data[1:], columns=furnace_data[0])
            df_furnace.to_excel(writer, sheet_name='小炉区数据', index=False)
            
            # 调节阀数据
            valve_data = []
            
            # 添加表头
            headers = ["小炉编号", "窑老期支管流量", "正常支管流量", "C计大", "C计小", "C选定", "K大", "K小"]
            valve_data.append(headers)
            
            # 添加小炉数据
            for i, furnace in enumerate(self.oxygen_furnace_data):
                row = [
                    i + 1,
                    furnace['old_branch_flow'].get(),
                    furnace['normal_branch_flow'].get(),
                    furnace['c_large'].get(),
                    furnace['c_small'].get(),
                    furnace['c_selected'].get(),
                    furnace['k_large'].get(),
                    furnace['k_small'].get()
                ]
                valve_data.append(row)
            
            # 转换为DataFrame并导出
            df_valve = pd.DataFrame(valve_data[1:], columns=valve_data[0])
            df_valve.to_excel(writer, sheet_name='调节阀数据', index=False)
            
            # 保存Excel文件
            writer.close()
            
            messagebox.showinfo("导出成功", f"数据已成功导出到 {file_path}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出数据时出错: {str(e)}")
    def load_oxygen_data_from_current_project(self):
        """从当前项目加载氧气管道计算数据"""
        try:
            # 获取项目名称和工程代号
            project_name = self.project_name.get()
            project_code = self.project_code.get()
            
            if not project_name and not project_code:
                return False
                
            # 读取历史记录
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                
                # 查找当前项目
                for record in history:
                    if ((record.get("工程名称") == project_name) or 
                        (project_code and record.get("工程代号") == project_code)):
                        
                        # 加载基本信息数据
                        if "氧气正常流量(Nm³/h)" in record:
                            self.oxygen_flow.set(record.get("氧气正常流量(Nm³/h)", ""))
                        if "窑老期流量(Nm³/h)" in record:
                            self.oxygen_old_flow.set(record.get("窑老期流量(Nm³/h)", ""))
                        if "氧气进车间压力(MPa)" in record:
                            self.oxygen_inlet_pressure.set(record.get("氧气进车间压力(MPa)", ""))
                        if "氧气总管调节阀前压力(MPa)" in record:
                            self.oxygen_main_pre_pressure.set(record.get("氧气总管调节阀前压力(MPa)", ""))
                        if "氧气总管调节阀后压力(MPa)" in record:
                            self.oxygen_main_post_pressure.set(record.get("氧气总管调节阀后压力(MPa)", ""))
                        if "氧气支管阀前压力(MPa)" in record:
                            self.oxygen_branch_pre_pressure.set(record.get("氧气支管阀前压力(MPa)", ""))
                        if "氧气支管阀后压力(MPa)" in record:
                            self.oxygen_branch_post_pressure.set(record.get("氧气支管阀后压力(MPa)", ""))
                        if "氧气设计流速(m/s)" in record:
                            self.oxygen_velocity.set(record.get("氧气设计流速(m/s)", ""))
                        if "氧气设计温度(℃)" in record:
                            self.oxygen_temperature.set(record.get("氧气设计温度(℃)", ""))
                        
                        # 加载计算结果
                        if "氧气工作状态下流量(Nm³/h)" in record:
                            self.oxygen_working_flow.set(record.get("氧气工作状态下流量(Nm³/h)", ""))
                        if "氧气窑老期工作状态下流量(Nm³/h)" in record:
                            self.oxygen_working_old_flow.set(record.get("氧气窑老期工作状态下流量(Nm³/h)", ""))
                        if "氧气进车间总管管径(mm)" in record:
                            self.oxygen_main_diameter.set(record.get("氧气进车间总管管径(mm)", ""))
                        if "氧气选取总管阀前管径(mm)" in record:
                            self.oxygen_selected_diameter.set(record.get("氧气选取总管阀前管径(mm)", ""))
                        if "氧气反算总管阀前流速(m/s)" in record:
                            self.oxygen_actual_velocity.set(record.get("氧气反算总管阀前流速(m/s)", ""))
                        if "氧气总管调节阀后管径(mm)" in record:
                            self.oxygen_main_post_calc_diameter.set(record.get("氧气总管调节阀后管径(mm)", ""))
                        if "氧气选取总管阀后管径(mm)" in record:
                            self.oxygen_main_post_selected_diameter.set(record.get("氧气选取总管阀后管径(mm)", ""))
                        if "氧气反算总管阀后流速(m/s)" in record:
                            self.oxygen_main_post_actual_velocity.set(record.get("氧气反算总管阀后流速(m/s)", ""))
                        
                        # 加载调节阀数据
                        if "氧气总管C计大" in record:
                            self.oxygen_main_c_large.set(record.get("氧气总管C计大", ""))
                        if "氧气总管C计小" in record:
                            self.oxygen_main_c_small.set(record.get("氧气总管C计小", ""))
                        if "氧气总管C选定" in record:
                            self.oxygen_main_c_selected.set(record.get("氧气总管C选定", ""))
                        if "氧气总管K大" in record:
                            self.oxygen_main_k_large.set(record.get("氧气总管K大", ""))
                        if "氧气总管K小" in record:
                            self.oxygen_main_k_small.set(record.get("氧气总管K小", ""))
                        if "氧气介质密度(kg/m³)" in record:
                            self.oxygen_density.set(record.get("氧气介质密度(kg/m³)", ""))
                        
                        # 加载小炉区数据
                        furnace_data = record.get("氧气小炉区数据", [])
                        for i, furnace in enumerate(furnace_data):
                            if i < len(self.oxygen_furnace_data):
                                if "平均热负荷" in furnace:
                                    self.oxygen_furnace_data[i]['heat_load'].set(furnace.get("平均热负荷", ""))
                                if "浮动值" in furnace:
                                    self.oxygen_furnace_data[i]['float_value'].set(furnace.get("浮动值", ""))
                                if "阀前流量" in furnace:
                                    self.oxygen_furnace_data[i]['pre_flow'].set(furnace.get("阀前流量", ""))
                                if "阀前计算管径" in furnace:
                                    self.oxygen_furnace_data[i]['pre_calc_diameter'].set(furnace.get("阀前计算管径", ""))
                                if "阀前选取管径" in furnace:
                                    self.oxygen_furnace_data[i]['pre_selected_diameter'].set(furnace.get("阀前选取管径", ""))
                                if "阀前反算流速" in furnace:
                                    self.oxygen_furnace_data[i]['pre_actual_velocity'].set(furnace.get("阀前反算流速", ""))
                                if "阀后流量" in furnace:
                                    self.oxygen_furnace_data[i]['post_flow'].set(furnace.get("阀后流量", ""))
                                if "阀后计算管径" in furnace:
                                    self.oxygen_furnace_data[i]['post_calc_diameter'].set(furnace.get("阀后计算管径", ""))
                                if "阀后选取管径" in furnace:
                                    self.oxygen_furnace_data[i]['post_selected_diameter'].set(furnace.get("阀后选取管径", ""))
                                if "阀后反算流速" in furnace:
                                    self.oxygen_furnace_data[i]['post_actual_velocity'].set(furnace.get("阀后反算流速", ""))
                                if "C计大" in furnace:
                                    self.oxygen_furnace_data[i]['c_large'].set(furnace.get("C计大", ""))
                                if "C计小" in furnace:
                                    self.oxygen_furnace_data[i]['c_small'].set(furnace.get("C计小", ""))
                                if "C选定" in furnace:
                                    self.oxygen_furnace_data[i]['c_selected'].set(furnace.get("C选定", ""))
                                if "K大" in furnace:
                                    self.oxygen_furnace_data[i]['k_large'].set(furnace.get("K大", ""))
                                if "K小" in furnace:
                                    self.oxygen_furnace_data[i]['k_small'].set(furnace.get("K小", ""))
                        
                        print(f"从项目'{project_name}'中加载氧气管道计算数据成功")
                        return True
                
                return False
            
            return False
        except Exception as e:
            print(f"加载氧气管道数据时出错: {str(e)}")
            traceback.print_exc()
            return False
    def load_data_from_current_project(self):
        """从当前项目加载数据"""
        # 原load_oxygen_data_from_current_project函数的实现
        
    def save_data(self, window):
        """保存数据到主项目"""
        # 原save_oxygen_data函数的实现
        
    def save_data_without_closing(self, window):
        """保存数据但不关闭窗口"""
        # 原save_oxygen_data_without_closing函数的实现