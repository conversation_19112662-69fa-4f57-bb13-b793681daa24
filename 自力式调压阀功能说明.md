# 自力式调压阀设备表自动更新功能说明

## 功能概述

本功能实现了根据自力式阀计算界面的数据，自动在设备表中添加和更新自力式调压阀设备的功能。

## 功能特点

### 1. 自动设备序号生成
- 设备序号为全启式安全阀序号+1
- 支持多个自力式阀，序号依次递增

### 2. 智能位号计算
- 位号是最后一个小炉顶部导向型单座调节阀位号+1
- 自动去掉A~B字母，只保留数字部分
- 例如：最后一个小炉调节阀位号是R-06(A~B)，则自力式调压阀位号为R-07

### 3. 标准设备信息
- 型号及规格：801P DN20 PN16 Cv=10 阀体碳钢材质
- 单位：台
- 数量：1台
- 设备来源：工装自控工程（无锡）有限公司

### 4. 动态子行信息
- **子行1**：介质信息和最大流量
  - 格式：介质：天然气 Qmax={最大流量}Nm3/h
  - 最大流量关联自力式阀计算界面的最大流量字段
  - 备注：配成套法兰及紧固密封件(见说明)

- **子行2**：阀前压力和阀后压力
  - 格式：阀前压力：{阀前压力}MPa 阀后压力：{阀后压力}MPa
  - 压力值关联自力式阀计算界面的阀前压力和阀后压力字段

### 5. 实时数据同步
- 当自力式阀计算界面的数据变化时，设备表自动更新
- 支持的触发字段：
  - 自力式阀描述
  - 最大流量
  - 阀前压力
  - 阀后压力

### 6. 多阀门支持
- 根据自力式阀计算界面的标签页数量自动创建对应数量的设备
- 新增标签页时自动添加设备
- 删除标签页时自动删除对应设备

## 使用方法

### 1. 首次使用
1. 打开自力式阀计算界面
2. 填写自力式阀1的相关数据（描述、最大流量、阀前压力、阀后压力等）
3. 系统自动在设备表中创建对应的自力式调压阀设备

### 2. 添加更多自力式阀
1. 在自力式阀计算界面点击"新增自力式阀计算"按钮
2. 填写新阀门的数据
3. 系统自动在设备表中添加新的自力式调压阀设备

### 3. 修改数据
1. 在自力式阀计算界面修改任何相关数据
2. 系统自动更新设备表中对应的信息

### 4. 删除阀门
1. 在自力式阀计算界面删除标签页
2. 系统自动从设备表中删除对应的设备

## 项目加载支持

- 首次加载项目时，系统自动识别自力式阀计算界面的数据
- 自动更新设备表中的自力式调压阀信息
- 确保数据的一致性和完整性

## 技术实现

### 1. 数据监听
- 使用tkinter的trace_add机制监听数据变化
- 实时响应用户输入

### 2. 设备表集成
- 与现有设备表管理系统无缝集成
- 遵循现有的设备序号和位号规则

### 3. 错误处理
- 完善的异常处理机制
- 当依赖数据不存在时优雅降级

## 注意事项

1. **依赖关系**：需要先有全启式安全阀和小炉顶部导向型单座调节阀，才能正确计算序号和位号
2. **数据完整性**：建议在填写自力式阀数据时确保所有必要字段都有值
3. **项目保存**：修改数据后建议及时保存项目，确保数据持久化

## 示例

假设当前设备表中：
- 全启式安全阀序号为：8
- 最后一个小炉调节阀位号为：R-06(A~B)

则自力式调压阀1的信息为：
- 序号：9
- 位号：R-07
- 设备名称：自力式调压阀
- 型号及规格：801P DN20 PN16 Cv=10 阀体碳钢材质
- 子行1：介质：天然气 Qmax=300Nm3/h
- 子行2：阀前压力：0.2MPa 阀后压力：0.06MPa

如果添加自力式阀2，则：
- 序号：10
- 位号：R-08
- 其他信息类似，但数据来源于自力式阀2的计算界面
